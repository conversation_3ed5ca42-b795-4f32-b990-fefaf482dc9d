import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react()
  ],
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          clerk: ['@clerk/clerk-react'],
          convex: ['convex/react'],
          ui: ['src/components/ui/index.ts'],
          maps: ['src/components/GoogleMaps/index.ts']
        }
      }
    }
  },
  server: {
    port: 5173,
    strictPort: true, // Fail if port 5173 is occupied instead of trying other ports
    host: 'localhost',
    open: false, // Don't auto-open browser to avoid conflicts
    hmr: {
      // Optimize HMR to prevent memory issues
      overlay: true,
      clientPort: 5173
    },
    watch: {
      // Optimize file watching to reduce memory usage
      usePolling: false,
      interval: 100,
      binaryInterval: 300,
      ignored: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.env*',
        '**/coverage/**',
        '**/.nyc_output/**',
        '**/.cache/**',
        '**/.temp/**',
        '**/.tmp/**'
      ]
    }
  },
  preview: {
    port: 4173,
    strictPort: true,
    host: 'localhost'
  },
  // Optimize build and dev performance
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@clerk/clerk-react'
    ],
    exclude: [
      'convex/server',
      'convex/_generated/server',
      'convex/_generated/api',
      'convex/react',
      'convex'
    ]
  },
  // Reduce memory usage during development
  define: {
    // Reduce bundle size in development
    __DEV__: JSON.stringify(true)
  }
})
