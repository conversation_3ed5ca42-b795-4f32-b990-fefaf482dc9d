# JobbLogg Production Environment Variables
# Copy this file to .env.production and configure your production values

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=production
COMPOSE_ENV=prod
BUILD_TARGET=production

# =============================================================================
# PORT CONFIGURATION
# =============================================================================
FRONTEND_PORT=5174
# Note: CONVEX_PORT not needed in production (using cloud Convex)

# =============================================================================
# CONVEX CONFIGURATION (Cloud Service)
# =============================================================================
CONVEX_DEPLOYMENT=prod:jobblogg
CONVEX_URL=https://standing-aardvark-575.convex.cloud
VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud

# =============================================================================
# CLERK AUTHENTICATION
# =============================================================================
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_production_clerk_key

# =============================================================================
# GOOGLE MAPS
# =============================================================================
VITE_GOOGLE_MAPS_API_KEY=your_production_google_maps_api_key

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_production_stripe_key
STRIPE_SECRET_KEY=sk_live_your_production_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_production_webhook_secret

# =============================================================================
# EMAIL SERVICE
# =============================================================================
RESEND_API_KEY=re_your_production_resend_key

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
VITE_ALLOW_INDEXING=true

# =============================================================================
# SECURITY NOTES
# =============================================================================
# - Never commit this file with real secrets to version control
# - Use environment-specific secrets management in production
# - Rotate keys regularly
# - Monitor for unauthorized access
