<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fiks Administrator <PERSON></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2563eb;
            margin-bottom: 20px;
        }
        .info-box {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .success-box {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .error-box {
            background: #fee2e2;
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .loading {
            display: none;
            color: #6b7280;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #e5e7eb;
        }
        .step.active {
            border-left-color: #2563eb;
            background: #f8fafc;
        }
        .step.completed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fiks Administrator Rolle</h1>
        
        <div class="info-box">
            <strong>Hva gjør denne siden?</strong><br>
            Hvis du opprettet en contractor company men ikke har administrator rettigheter, 
            kan du bruke denne siden for å fikse det.
        </div>

        <div class="warning-box">
            <strong>⚠️ Viktig:</strong><br>
            • Du må være logget inn i JobbLogg<br>
            • Du må være eier av en contractor company<br>
            • Dette fungerer kun for company owners, ikke team medlemmer
        </div>

        <div id="steps">
            <div class="step" id="step1">
                <strong>Steg 1:</strong> Sjekk om du er logget inn
                <button onclick="checkAuth()">Sjekk innlogging</button>
            </div>
            
            <div class="step" id="step2">
                <strong>Steg 2:</strong> Sjekk din nåværende rolle
                <button onclick="checkCurrentRole()" disabled>Sjekk rolle</button>
            </div>
            
            <div class="step" id="step3">
                <strong>Steg 3:</strong> Fiks administrator rolle
                <button onclick="fixAdminRole()" disabled>Fiks rolle</button>
            </div>
        </div>

        <div id="result"></div>
        <div class="loading" id="loading">⏳ Arbeider...</div>

        <div class="info-box" style="margin-top: 30px;">
            <strong>💡 Tips:</strong><br>
            Etter at administrator rolle er satt, kan du gå til JobbLogg og se "Team administrasjon" 
            i menyen for å administrere teammedlemmer.
        </div>
    </div>

    <script>
        let currentUser = null;
        let convexClient = null;

        // Initialize Convex client
        async function initConvex() {
            try {
                // Import Convex client
                const { ConvexHttpClient } = await import('https://cdn.skypack.dev/convex/browser');
                convexClient = new ConvexHttpClient('https://jolly-shark-158.convex.cloud');
                return true;
            } catch (error) {
                console.error('Failed to initialize Convex:', error);
                return false;
            }
        }

        async function checkAuth() {
            showLoading(true);
            
            try {
                // Check if user is logged in via Clerk
                if (window.Clerk) {
                    const user = window.Clerk.user;
                    if (user) {
                        currentUser = user;
                        showResult('✅ Du er logget inn som: ' + user.emailAddresses[0].emailAddress, 'success');
                        completeStep('step1');
                        enableStep('step2');
                    } else {
                        showResult('❌ Du er ikke logget inn. Gå til JobbLogg og logg inn først.', 'error');
                    }
                } else {
                    showResult('❌ Clerk ikke lastet. Åpne denne siden fra JobbLogg.', 'error');
                }
            } catch (error) {
                showResult('❌ Feil ved sjekking av innlogging: ' + error.message, 'error');
            }
            
            showLoading(false);
        }

        async function checkCurrentRole() {
            if (!currentUser) {
                showResult('❌ Du må være logget inn først.', 'error');
                return;
            }

            showLoading(true);
            
            try {
                if (!convexClient) {
                    await initConvex();
                }

                // This would require authentication, so we'll just enable the next step
                showResult('ℹ️ Klar til å fikse administrator rolle.', 'info');
                completeStep('step2');
                enableStep('step3');
            } catch (error) {
                showResult('❌ Feil ved sjekking av rolle: ' + error.message, 'error');
            }
            
            showLoading(false);
        }

        async function fixAdminRole() {
            if (!currentUser) {
                showResult('❌ Du må være logget inn først.', 'error');
                return;
            }

            showLoading(true);
            
            try {
                // Call the fix function via Convex
                const result = await convexClient.mutation('contractorOnboarding:fixMissingAdministratorRole', {
                    clerkUserId: currentUser.id
                });

                if (result.success) {
                    if (result.alreadyAdmin) {
                        showResult('ℹ️ ' + result.message, 'info');
                    } else {
                        showResult('✅ ' + result.message + '! Du kan nå gå til JobbLogg og se "Team administrasjon" i menyen.', 'success');
                    }
                    completeStep('step3');
                } else {
                    showResult('❌ Kunne ikke fikse administrator rolle.', 'error');
                }
            } catch (error) {
                showResult('❌ Feil ved fiksing av rolle: ' + error.message, 'error');
            }
            
            showLoading(false);
        }

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = `<div class="${type}-box">${message}</div>`;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function completeStep(stepId) {
            document.getElementById(stepId).classList.add('completed');
        }

        function enableStep(stepId) {
            const step = document.getElementById(stepId);
            step.classList.add('active');
            const button = step.querySelector('button');
            if (button) button.disabled = false;
        }

        // Initialize when page loads
        window.addEventListener('load', async () => {
            await initConvex();
        });
    </script>
</body>
</html>
