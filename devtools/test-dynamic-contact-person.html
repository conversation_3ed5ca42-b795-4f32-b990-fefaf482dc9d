<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg - Dynamic Contact Person Enhancement</title>
    <style>
        body {
            font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        .header h1 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            color: #6b7280;
            font-size: 1.1rem;
            margin: 0;
        }
        .success-banner {
            background: #f0fdf4;
            border: 2px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .success-banner h2 {
            color: #166534;
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }
        .success-banner p {
            color: #15803d;
            margin: 0;
            font-weight: 500;
        }
        .feature-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .feature-section h2 {
            color: #1f2937;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        .feature-card h3 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .feature-card p {
            color: #4b5563;
            margin: 0 0 15px 0;
            font-size: 0.95rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            margin: 15px 0;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-after .before {
            background: #fef2f2;
            border: 2px solid #fecaca;
            border-radius: 8px;
            padding: 15px;
        }
        .before-after .after {
            background: #f0fdf4;
            border: 2px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
        }
        .before-after h4 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            font-weight: 600;
        }
        .before-after .before h4 {
            color: #991b1b;
        }
        .before-after .after h4 {
            color: #166534;
        }
        .role-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .role-example {
            background: #eff6ff;
            border: 2px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .role-example h4 {
            color: #1e40af;
            margin: 0 0 5px 0;
            font-size: 1rem;
            font-weight: 600;
        }
        .role-example p {
            color: #1e3a8a;
            margin: 0;
            font-size: 0.9rem;
        }
        .test-instructions {
            background: #eff6ff;
            border: 2px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-instructions h3 {
            color: #1e40af;
            margin: 0 0 15px 0;
            font-size: 1.3rem;
        }
        .test-instructions ol {
            color: #1e3a8a;
            margin: 0;
            padding-left: 20px;
        }
        .test-instructions li {
            margin: 8px 0;
            font-weight: 500;
        }
        .lock-demo {
            background: #f9fafb;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .lock-demo h4 {
            color: #374151;
            margin: 0 0 15px 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        .locked-field {
            background: #f3f4f6;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .lock-icon {
            width: 20px;
            height: 20px;
            color: #6b7280;
        }
        .field-content {
            flex: 1;
        }
        .field-label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }
        .field-value {
            color: #1f2937;
            font-size: 1rem;
        }
        .field-helper {
            color: #6b7280;
            font-size: 0.8rem;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👨‍💼 Dynamic Contact Person Enhancement</h1>
            <p>Enhanced Step 3 with dynamic labels and locked registry fields</p>
        </div>

        <div class="success-banner">
            <h2>✅ ENHANCEMENT COMPLETE</h2>
            <p>Contact person field now adapts dynamically to registry data with locked auto-population</p>
        </div>

        <!-- Feature 1: Dynamic Labels -->
        <div class="feature-section">
            <h2>🏷️ Dynamic Field Labels</h2>
            <p>The contact person field label now changes dynamically based on the role type found in Brønnøysundregisteret.</p>
            
            <div class="role-examples">
                <div class="role-example">
                    <h4>DAGL</h4>
                    <p>Daglig leder</p>
                </div>
                <div class="role-example">
                    <h4>INNH</h4>
                    <p>Innehaver</p>
                </div>
                <div class="role-example">
                    <h4>BEST</h4>
                    <p>Bestyrende direktør</p>
                </div>
                <div class="role-example">
                    <h4>ADMD</h4>
                    <p>Administrerende direktør</p>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔍 Enhanced API Integration</h3>
                    <div class="code-block">
// Enhanced role detection
const roleTypesToCheck = [
  { code: 'DAGL', description: 'Daglig leder' },
  { code: 'INNH', description: 'Innehaver' },
  { code: 'BEST', description: 'Bestyrende direktør' },
  { code: 'ADMD', description: 'Administrerende direktør' }
];

// Return role type and description
return {
  firstName: navn.fornavn || '',
  lastName: navn.etternavn || '',
  fullName: fullName,
  roleType: roleType.code,
  roleDescription: roleType.description
};
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🎯 Dynamic Label Logic</h3>
                    <div class="code-block">
// Get dynamic label based on registry data
const getContactPersonLabel = (): string => {
  if (brregData?.managingDirector?.roleDescription) {
    return brregData.managingDirector.roleDescription;
  }
  return 'Daglig leder'; // Default fallback
};

// Dynamic field description
const getContactPersonDescription = (): string => {
  const label = getContactPersonLabel();
  return `Navn på ${label.toLowerCase()} som skal være hovedkontakt`;
};
                    </div>
                </div>
            </div>
        </div>

        <!-- Feature 2: Locked Fields -->
        <div class="feature-section">
            <h2>🔒 Locked Auto-populated Fields</h2>
            <p>Contact person fields auto-populated from Brønnøysundregisteret are now completely locked and non-editable.</p>
            
            <div class="before-after">
                <div class="before">
                    <h4>❌ Before</h4>
                    <ul class="feature-list">
                        <li>Toggle switch to override registry data</li>
                        <li>Users could modify verified information</li>
                        <li>Potential data integrity issues</li>
                        <li>Confusion about data source</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ After</h4>
                    <ul class="feature-list">
                        <li>Completely locked when auto-populated</li>
                        <li>Clear indication of registry source</li>
                        <li>Data integrity preserved</li>
                        <li>No user confusion</li>
                    </ul>
                </div>
            </div>

            <div class="lock-demo">
                <h4>Locked Field Example:</h4>
                <div class="locked-field">
                    <svg class="lock-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <div class="field-content">
                        <div class="field-label">Innehaver</div>
                        <div class="field-value">Arne Løken</div>
                        <div class="field-helper">Hentet automatisk fra Brønnøysundregisteret og kan ikke endres</div>
                    </div>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🛡️ Data Integrity</h3>
                    <ul class="feature-list">
                        <li>Registry-verified data cannot be modified</li>
                        <li>Prevents accidental data corruption</li>
                        <li>Maintains official business information</li>
                        <li>Ensures compliance with registry data</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🎨 UI Implementation</h3>
                    <div class="code-block">
{autoPopulatedFields.contactPerson ? (
  &lt;LockedInput
    label={getContactPersonLabel()}
    value={formData.contactPerson}
    fullWidth
    helperText="Hentet automatisk fra Brønnøysundregisteret og kan ikke endres"
  /&gt;
) : (
  &lt;TextInput
    label={getContactPersonLabel()}
    // ... manual entry props
  /&gt;
)}
                    </div>
                </div>
            </div>
        </div>

        <!-- Feature 3: Enhanced User Experience -->
        <div class="feature-section">
            <h2>✨ Enhanced User Experience</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎯 Context-Aware Interface</h3>
                    <ul class="feature-list">
                        <li>Field labels match actual business roles</li>
                        <li>Validation messages use correct terminology</li>
                        <li>Help text adapts to role type</li>
                        <li>Information section uses dynamic labels</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔄 Simplified State Management</h3>
                    <ul class="feature-list">
                        <li>Removed contact person from override state</li>
                        <li>Cleaner component logic</li>
                        <li>Reduced complexity</li>
                        <li>Better maintainability</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>📱 Consistent Behavior</h3>
                    <ul class="feature-list">
                        <li>Phone and email still have override toggles</li>
                        <li>Contact person is always locked when available</li>
                        <li>Clear distinction between field types</li>
                        <li>Predictable user interaction</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🌍 Norwegian Localization</h3>
                    <ul class="feature-list">
                        <li>Proper Norwegian business terminology</li>
                        <li>Role-specific validation messages</li>
                        <li>Contextual help text</li>
                        <li>Professional language throughout</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="test-instructions">
            <h3>🧪 Testing Instructions</h3>
            <ol>
                <li><strong>Test Different Company Types:</strong> Search for companies with different role types (AS, ENK, etc.)</li>
                <li><strong>Verify Dynamic Labels:</strong> Check that field labels change based on registry role data</li>
                <li><strong>Test Locked Fields:</strong> Confirm auto-populated contact person fields cannot be edited</li>
                <li><strong>Verify No Toggle:</strong> Ensure there's no override toggle for contact person field</li>
                <li><strong>Test Manual Entry:</strong> For companies without registry data, verify manual entry works</li>
                <li><strong>Check Validation:</strong> Verify error messages use correct role terminology</li>
                <li><strong>Test Phone/Email Toggles:</strong> Confirm phone and email still have override functionality</li>
            </ol>
        </div>

        <!-- Technical Implementation -->
        <div class="feature-section">
            <h2>⚙️ Technical Implementation</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📊 Enhanced CompanyInfo Interface</h3>
                    <div class="code-block">
managingDirector?: {
  firstName: string;
  lastName: string;
  fullName: string;
  birthDate?: string;
  roleType?: string;        // 'DAGL', 'INNH', etc.
  roleDescription?: string; // 'Daglig leder', 'Innehaver', etc.
};
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🔧 API Enhancement</h3>
                    <div class="code-block">
// Priority-based role detection
for (const roleType of roleTypesToCheck) {
  const roleGroup = rolesData.rollegrupper?.find((group: any) =>
    group.type?.kode === roleType.code
  );
  
  if (roleGroup?.roller?.[0]?.person) {
    // Return first matching role with type info
    return { ...personData, roleType: roleType.code, roleDescription: roleType.description };
  }
}
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🎛️ State Management</h3>
                    <div class="code-block">
// Removed contactPerson from fieldOverrides
const [fieldOverrides, setFieldOverrides] = useState&lt;{
  phone: boolean;
  email: boolean;
}&gt;({ phone: false, email: false });

// Contact person is always locked when auto-populated
if (brregData.managingDirector?.fullName && !formData.contactPerson) {
  updates.contactPerson = brregData.managingDirector.fullName;
  newAutoPopulated.contactPerson = true;
}
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>📁 Files Modified</h3>
                    <ul class="feature-list">
                        <li>src/services/companyLookup.ts</li>
                        <li>src/pages/ContractorOnboarding/steps/Step3ContactDetails.tsx</li>
                        <li>Enhanced API role detection</li>
                        <li>Dynamic UI components</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Success Banner -->
        <div class="success-banner">
            <h2>🎉 ENHANCEMENT COMPLETE</h2>
            <p>Dynamic contact person fields with locked registry data and role-specific labels!</p>
        </div>
    </div>
</body>
</html>
