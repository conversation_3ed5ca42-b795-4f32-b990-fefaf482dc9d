<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg Typing Indicators Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #2563EB;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        #console-output {
            background-color: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔍 JobbLogg Typing Indicators Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <div class="info status">
            <strong>How to test typing indicators:</strong><br>
            1. Open the main JobbLogg application in another tab<br>
            2. Navigate to a project with chat functionality<br>
            3. Open the chat in a log entry<br>
            4. Start typing in the message input<br>
            5. Check the console output below for debug messages<br>
            6. Look for typing indicators appearing in the chat
        </div>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <ul>
            <li>✅ When you start typing, "handleTypingStart" should be called</li>
            <li>✅ Backend should receive startTyping mutation</li>
            <li>✅ Other users should see typing indicator appear</li>
            <li>✅ When you stop typing, indicator should disappear after timeout</li>
            <li>✅ Multiple users typing should show combined indicator</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Debug Console Output</h2>
        <button onclick="clearConsole()">Clear Console</button>
        <button onclick="testTypingFlow()">Test Typing Flow</button>
        <div id="console-output"></div>
    </div>

    <script>
        // Capture console logs
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : '#00ff00';
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        function testTypingFlow() {
            console.log('🧪 Starting typing indicator test...');
            console.log('🔍 Check the main application for typing functionality');
            console.log('📝 Look for these debug messages:');
            console.log('   🟢 MessageInput - useEffect triggered');
            console.log('   🟢 MessageInput - Starting typing indicator');
            console.log('   🔵 EmbeddedChat - handleTypingStart called');
            console.log('   🔵 EmbeddedChat - Calling startTypingMutation');
            console.log('   🟡 EmbeddedChat - Typing indicators data');
            console.log('   🔴 EmbeddedChat - handleTypingStop called');
        }
        
        // Initial message
        console.log('🚀 Typing indicators debug console ready!');
        console.log('📱 Open JobbLogg in another tab and test typing functionality');
    </script>
</body>
</html>
