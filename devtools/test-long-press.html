<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Long-Press Functionality - JobbLogg</title>
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            min-height: 44px;
            min-width: 120px;
        }
        .test-button:hover {
            background: #1d4ed8;
        }
        .instructions {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
            line-height: 1.5;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
            min-height: 20px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .timer {
            font-family: monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Long-Press Functionality Test</h1>
        <p>Test the long-press functionality for the "Liker" button in JobbLogg's chat system.</p>

        <!-- Desktop Long-Press Test -->
        <div class="test-section">
            <h2>Desktop Long-Press Test</h2>
            <div class="instructions">
                <strong>Instructions:</strong>
                <ul>
                    <li><strong>Single Click:</strong> Click and release quickly (under 400ms) - should trigger default reaction</li>
                    <li><strong>Long Press:</strong> Click and hold for 450ms+ - should show emoji palette</li>
                    <li>Test with mouse events on desktop browsers</li>
                </ul>
            </div>
            
            <button id="desktop-test" class="test-button">Desktop Test Button</button>
            <div id="desktop-result" class="result"></div>
        </div>

        <!-- Mobile Touch Test -->
        <div class="test-section">
            <h2>Mobile Touch Test</h2>
            <div class="instructions">
                <strong>Instructions:</strong>
                <ul>
                    <li><strong>Touch:</strong> Tap the button - should show bottom sheet immediately</li>
                    <li><strong>Long Touch:</strong> Touch and hold - should show bottom sheet with drag support</li>
                    <li>Test on mobile devices or browser dev tools mobile mode</li>
                </ul>
            </div>
            
            <button id="mobile-test" class="test-button">Mobile Test Button</button>
            <div id="mobile-result" class="result"></div>
        </div>

        <!-- Timing Test -->
        <div class="test-section">
            <h2>Timing Precision Test</h2>
            <div class="instructions">
                <strong>Instructions:</strong>
                <ul>
                    <li>This test measures exact timing of press duration</li>
                    <li>Try different hold durations: 100ms, 300ms, 450ms, 600ms</li>
                    <li>Verify 450ms threshold works correctly</li>
                </ul>
            </div>
            
            <button id="timing-test" class="test-button">Timing Test Button</button>
            <div id="timing-result" class="result"></div>
            <div id="timing-display" class="timer"></div>
        </div>
    </div>

    <script>
        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   window.innerWidth <= 768;
        }

        // Desktop Test
        let desktopTimer = null;
        let desktopStartTime = null;
        let desktopIsLongPress = false;

        const desktopButton = document.getElementById('desktop-test');
        
        desktopButton.addEventListener('mousedown', (e) => {
            e.preventDefault();
            desktopStartTime = Date.now();
            desktopIsLongPress = false;
            
            showResult('desktop-result', 'Mouse down detected, starting timer...', 'info');
            
            desktopTimer = setTimeout(() => {
                desktopIsLongPress = true;
                showResult('desktop-result', '✅ Long press detected (450ms+) - Emoji palette should appear', 'success');
                
                // Simulate haptic feedback
                if ('vibrate' in navigator) {
                    navigator.vibrate(50);
                }
            }, 450);
        });

        desktopButton.addEventListener('mouseup', () => {
            if (desktopTimer) {
                clearTimeout(desktopTimer);
                desktopTimer = null;
            }
            
            const duration = desktopStartTime ? Date.now() - desktopStartTime : 0;
            
            setTimeout(() => {
                if (!desktopIsLongPress && duration < 400) {
                    showResult('desktop-result', `✅ Single click detected (${duration}ms) - Default reaction (👍) should trigger`, 'success');
                } else if (duration >= 400) {
                    showResult('desktop-result', `⚠️ Long press completed (${duration}ms) - Palette should be visible`, 'info');
                }
                desktopIsLongPress = false;
            }, 100);
        });

        desktopButton.addEventListener('mouseleave', () => {
            if (desktopTimer) {
                clearTimeout(desktopTimer);
                desktopTimer = null;
                showResult('desktop-result', '❌ Mouse left button area - Long press cancelled', 'error');
            }
        });

        desktopButton.addEventListener('click', (e) => {
            if (desktopIsLongPress) {
                e.preventDefault();
                showResult('desktop-result', '🔄 Click prevented (was long press)', 'info');
            }
        });

        // Mobile Test
        let mobileTimer = null;
        let mobileStartTime = null;
        let mobileIsLongPress = false;

        const mobileButton = document.getElementById('mobile-test');
        
        mobileButton.addEventListener('touchstart', (e) => {
            e.preventDefault();
            mobileStartTime = Date.now();
            mobileIsLongPress = false;
            
            showResult('mobile-result', 'Touch start detected...', 'info');
            
            mobileTimer = setTimeout(() => {
                mobileIsLongPress = true;
                showResult('mobile-result', '✅ Long touch detected - Bottom sheet with drag support should appear', 'success');
                
                if ('vibrate' in navigator) {
                    navigator.vibrate(50);
                }
            }, 450);
        });

        mobileButton.addEventListener('touchend', () => {
            if (mobileTimer) {
                clearTimeout(mobileTimer);
                mobileTimer = null;
            }
            
            const duration = mobileStartTime ? Date.now() - mobileStartTime : 0;
            
            setTimeout(() => {
                if (!mobileIsLongPress) {
                    showResult('mobile-result', `✅ Touch tap detected (${duration}ms) - Bottom sheet should appear immediately`, 'success');
                } else {
                    showResult('mobile-result', `✅ Long touch completed (${duration}ms) - Bottom sheet should be visible`, 'success');
                }
                mobileIsLongPress = false;
            }, 100);
        });

        mobileButton.addEventListener('click', () => {
            if (!mobileIsLongPress) {
                showResult('mobile-result', '📱 Mobile click - Bottom sheet should open', 'success');
            }
        });

        // Timing Test
        let timingTimer = null;
        let timingStartTime = null;
        let timingInterval = null;

        const timingButton = document.getElementById('timing-test');
        const timingDisplay = document.getElementById('timing-display');
        
        timingButton.addEventListener('mousedown', (e) => {
            e.preventDefault();
            timingStartTime = Date.now();
            
            showResult('timing-result', 'Timing test started...', 'info');
            
            // Update display every 10ms
            timingInterval = setInterval(() => {
                const elapsed = Date.now() - timingStartTime;
                timingDisplay.textContent = `Elapsed: ${elapsed}ms`;
                
                if (elapsed >= 450 && !timingTimer) {
                    showResult('timing-result', '🎯 450ms threshold reached!', 'success');
                }
            }, 10);
            
            timingTimer = setTimeout(() => {
                showResult('timing-result', '✅ Long press threshold (450ms) triggered!', 'success');
            }, 450);
        });

        timingButton.addEventListener('mouseup', () => {
            if (timingTimer) {
                clearTimeout(timingTimer);
                timingTimer = null;
            }
            
            if (timingInterval) {
                clearInterval(timingInterval);
                timingInterval = null;
            }
            
            const duration = timingStartTime ? Date.now() - timingStartTime : 0;
            timingDisplay.textContent = `Final duration: ${duration}ms`;
            
            if (duration < 450) {
                showResult('timing-result', `⚡ Quick release (${duration}ms) - Below threshold`, 'info');
            } else {
                showResult('timing-result', `⏱️ Long press completed (${duration}ms) - Above threshold`, 'success');
            }
        });

        // Initial setup
        showResult('desktop-result', 'Ready for desktop testing');
        showResult('mobile-result', 'Ready for mobile testing');
        showResult('timing-result', 'Ready for timing testing');
        
        // Display device info
        const deviceInfo = isMobile() ? 'Mobile device detected' : 'Desktop device detected';
        console.log(deviceInfo);
    </script>
</body>
</html>
