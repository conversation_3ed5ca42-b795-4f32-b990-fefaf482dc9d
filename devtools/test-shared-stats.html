<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Shared Project Statistics - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #6b7280;
            margin-bottom: 24px;
        }
        .step {
            background: #eff6ff;
            border-left: 4px solid #2563eb;
            padding: 16px;
            margin: 16px 0;
        }
        .step h3 {
            margin: 0 0 8px 0;
            color: #1e40af;
        }
        .feature-list {
            background: #f0fdf4;
            border-left: 4px solid #10b981;
            padding: 16px;
            margin: 16px 0;
        }
        .feature-list h3 {
            margin: 0 0 8px 0;
            color: #047857;
        }
        .feature-list ul {
            margin: 8px 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 4px 0;
            color: #065f46;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button.secondary {
            background: #6b7280;
        }
        button.secondary:hover {
            background: #4b5563;
        }
        .links {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 16px;
        }
        .links a {
            color: #2563eb;
            text-decoration: none;
            padding: 8px 12px;
            border: 1px solid #2563eb;
            border-radius: 6px;
            font-size: 14px;
        }
        .links a:hover {
            background: #2563eb;
            color: white;
        }
        .result {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
        }
        .result.success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Test Enhanced Shared Project Statistics</h1>
        <p class="subtitle">Test the improved shared project statistics display on the JobbLogg dashboard</p>

        <div class="feature-list">
            <h3>✨ New Features Implemented</h3>
            <ul>
                <li><strong>Enhanced View Count Display:</strong> Shows "X visninger" with proper Norwegian pluralization</li>
                <li><strong>Intelligent Time Formatting:</strong> Recent access shows relative time ("2 timer siden")</li>
                <li><strong>Norwegian Date/Time:</strong> Older access shows absolute time ("2. jul 2025 14:30")</li>
                <li><strong>Smart Fallback:</strong> Shows project count when no access data available</li>
                <li><strong>Contextual Information:</strong> Different formats based on how recent the access was</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 1: Create or Access Shared Project</h3>
            <p>First, ensure you have a shared project that customers can access to generate statistics.</p>
            
            <button onclick="openDashboard()">Open Dashboard</button>
            <button class="secondary" onclick="openCreateProject()">Create New Project</button>
            
            <p style="margin-top: 12px; font-size: 14px; color: #6b7280;">
                If you don't have any projects yet, create one first, then enable sharing in the project details.
            </p>
        </div>

        <div class="step">
            <h3>Step 2: Enable Project Sharing</h3>
            <p>Make sure at least one project has sharing enabled to generate statistics.</p>
            
            <ol style="margin: 12px 0; padding-left: 20px;">
                <li>Go to a project detail page</li>
                <li>Click "Del prosjekt" or sharing button</li>
                <li>Enable sharing and copy the shared link</li>
                <li>Visit the shared link to generate access statistics</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 3: Generate Access Statistics</h3>
            <p>Visit shared project links to create access data that will be displayed on the dashboard.</p>
            
            <button onclick="simulateAccess()">Simulate Customer Access</button>
            <button class="secondary" onclick="checkStats()">Check Dashboard Stats</button>
            
            <p style="margin-top: 12px; font-size: 14px; color: #6b7280;">
                Open shared project links in incognito/private browsing to simulate customer access.
            </p>
        </div>

        <div class="step">
            <h3>Step 4: Verify Enhanced Display</h3>
            <p>Check the dashboard to see the enhanced shared project statistics card.</p>
            
            <div style="background: #f8fafc; padding: 12px; border-radius: 6px; margin: 12px 0;">
                <strong>Expected Display Formats:</strong>
                <ul style="margin: 8px 0; padding-left: 20px; font-size: 14px;">
                    <li><strong>Recent access:</strong> "5 visninger" + "Sist åpnet: 2 timer siden"</li>
                    <li><strong>Yesterday:</strong> "3 visninger" + "Sist åpnet: I går 14:30"</li>
                    <li><strong>Older access:</strong> "8 visninger" + "Sist åpnet: 1. jul 15:45"</li>
                    <li><strong>No access data:</strong> "2 visninger" + "2 delte prosjekter"</li>
                </ul>
            </div>
        </div>

        <div class="links">
            <a href="http://localhost:5173" target="_blank">Dashboard</a>
            <a href="http://localhost:5173/create" target="_blank">Create Project</a>
            <a href="http://localhost:5173/test-unread-comments.html" target="_blank">Test Unread Comments</a>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(message, isSuccess = false) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.style.display = 'block';
            result.className = 'result';
            if (isSuccess) result.className += ' success';
        }

        function openDashboard() {
            window.open('http://localhost:5173', '_blank');
            showResult('✅ Opened dashboard. Look for the "Delte prosjekter" card in the stats section.\n\nThe card should show enhanced formatting with view count and last access time.', true);
        }

        function openCreateProject() {
            window.open('http://localhost:5173/create', '_blank');
            showResult('✅ Opened project creation page. Create a project, then enable sharing to generate statistics.', true);
        }

        function simulateAccess() {
            showResult(`🔄 To simulate customer access:

1. Create a project and enable sharing
2. Copy the shared project link (format: /shared/[sharedId])
3. Open the shared link in an incognito/private browser window
4. This will increment the access count and update the last accessed time
5. Return to the dashboard to see updated statistics

The dashboard will show the enhanced formatting based on when the access occurred.`);
        }

        function checkStats() {
            window.open('http://localhost:5173', '_blank');
            showResult(`📊 Check the dashboard for the enhanced shared project statistics:

✅ Look for the "Delte prosjekter" card (appears when no unread comments exist)
✅ Verify the view count shows proper Norwegian pluralization
✅ Check the subtitle for intelligent time formatting:
   - Recent: "Sist åpnet: X min/timer siden"
   - Yesterday: "Sist åpnet: I går HH:MM"
   - Older: "Sist åpnet: dd. mmm yyyy HH:MM"

The card should only appear when there are shared projects but no unread comments.`, true);
        }
    </script>
</body>
</html>
