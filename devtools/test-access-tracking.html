<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Access Tracking - JobbLogg</title>
    <style>
        body {
            font-family: Inter, system-ui, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #2563EB;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 8px;
        }
        .button:hover {
            background: #1D4ED8;
        }
        .input {
            width: 100%;
            padding: 12px;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            margin-bottom: 16px;
        }
        .result {
            background: #F3F4F6;
            padding: 16px;
            border-radius: 8px;
            margin-top: 16px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #ECFDF5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .error {
            background: #FEF2F2;
            border: 1px solid #DC2626;
            color: #991B1B;
        }
    </style>
</head>
<body>
    <h1>🧪 Access Tracking Test - JobbLogg</h1>
    
    <div class="card">
        <h2>Test Shared Project Access Tracking</h2>
        <p>This tool helps test the shared project access tracking functionality.</p>
        
        <label for="sharedId">Shared ID:</label>
        <input type="text" id="sharedId" class="input" placeholder="Enter shared project ID (e.g., abc123xyz)" />
        
        <button class="button" onclick="testAccessTracking()">Test Access Tracking</button>
        <button class="button" onclick="simulateCustomerVisit()">Simulate Customer Visit</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <div class="card">
        <h2>Instructions</h2>
        <ol>
            <li>First, log into JobbLogg and create/share a project</li>
            <li>Copy the shared ID from the sharing URL (the part after /shared/)</li>
            <li>Paste it in the input field above</li>
            <li>Click "Test Access Tracking" to verify the mutation works</li>
            <li>Click "Simulate Customer Visit" to test the full flow</li>
        </ol>
    </div>

    <script>
        const CONVEX_URL = 'https://standing-aardvark-575.convex.cloud';
        
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = message;
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function testAccessTracking() {
            const sharedId = document.getElementById('sharedId').value.trim();
            if (!sharedId) {
                showResult('Please enter a shared ID', true);
                return;
            }

            try {
                showResult('Testing access tracking...');
                
                // Test the mutation directly
                const response = await fetch(`${CONVEX_URL}/api/mutation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        path: 'projects:trackSharedProjectAccess',
                        args: { sharedId }
                    })
                });

                const result = await response.json();
                
                if (result.success !== undefined) {
                    showResult(`✅ Access tracking successful!\n\nResult: ${JSON.stringify(result, null, 2)}`);
                } else if (result.error) {
                    showResult(`❌ Error: ${result.error}`, true);
                } else {
                    showResult(`📊 Response: ${JSON.stringify(result, null, 2)}`);
                }
            } catch (error) {
                showResult(`❌ Network error: ${error.message}`, true);
            }
        }

        async function simulateCustomerVisit() {
            const sharedId = document.getElementById('sharedId').value.trim();
            if (!sharedId) {
                showResult('Please enter a shared ID', true);
                return;
            }

            try {
                showResult('Simulating customer visit...');
                
                // Open the shared project page in a new tab
                const sharedUrl = `http://localhost:5173/shared/${sharedId}`;
                window.open(sharedUrl, '_blank');
                
                showResult(`✅ Opened shared project in new tab: ${sharedUrl}\n\nCheck the browser console for access tracking logs.`);
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
