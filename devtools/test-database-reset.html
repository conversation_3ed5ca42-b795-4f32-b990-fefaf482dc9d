<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg - Database Reset System Test</title>
    <style>
        body {
            font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        .header h1 {
            color: #dc2626;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            color: #6b7280;
            font-size: 1.1rem;
            margin: 0;
        }
        .warning-banner {
            background: #fef2f2;
            border: 2px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .warning-banner h2 {
            color: #dc2626;
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }
        .warning-banner p {
            color: #991b1b;
            margin: 0;
            font-weight: 500;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .test-section h2 {
            color: #1f2937;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        .feature-card h3 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .feature-card p {
            color: #4b5563;
            margin: 0 0 15px 0;
            font-size: 0.95rem;
        }
        .command-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            margin: 15px 0;
        }
        .command-block .comment {
            color: #9ca3af;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-safe {
            background: #dcfce7;
            color: #166534;
        }
        .status-destructive {
            background: #fef2f2;
            color: #dc2626;
        }
        .status-analysis {
            background: #dbeafe;
            color: #1d4ed8;
        }
        .deletion-order {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            margin: 20px 0;
        }
        .deletion-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f3f4f6;
            border-radius: 6px;
        }
        .deletion-step-number {
            background: #dc2626;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .deletion-step-content h4 {
            margin: 0 0 5px 0;
            color: #1f2937;
            font-weight: 600;
        }
        .deletion-step-content p {
            margin: 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .usage-examples {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .usage-examples h3 {
            color: #1d4ed8;
            margin: 0 0 15px 0;
        }
        .example-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #2563eb;
        }
        .example-item h4 {
            color: #1f2937;
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        .example-item p {
            color: #4b5563;
            margin: 0 0 10px 0;
            font-size: 0.9rem;
        }
        .highlight {
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            color: #92400e;
            font-weight: 600;
        }
        .safety-checklist {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .safety-checklist h3 {
            color: #166534;
            margin: 0 0 15px 0;
        }
        .safety-checklist ul {
            margin: 0;
            padding-left: 20px;
        }
        .safety-checklist li {
            color: #15803d;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗑️ Database Reset System</h1>
            <p>Comprehensive database reset tools for JobbLogg development</p>
            <div style="margin-top: 15px;">
                <span class="status-badge status-destructive">⚠️ DESTRUCTIVE</span>
                <span class="status-badge status-safe">✅ SCHEMA SAFE</span>
                <span class="status-badge status-analysis">🔍 DRY-RUN CAPABLE</span>
            </div>
        </div>

        <div class="warning-banner">
            <h2>⚠️ DEVELOPMENT ONLY</h2>
            <p>This system permanently deletes all user data. Only use in development/testing environments!</p>
        </div>

        <!-- System Overview -->
        <div class="test-section">
            <h2>📋 System Overview</h2>
            <p>The comprehensive database reset system safely removes all user-generated data while preserving database schema structure and system integrity.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔒 Safety Features</h3>
                    <p>Multiple layers of protection against accidental data loss</p>
                    <ul style="margin: 0; padding-left: 20px; color: #6b7280; font-size: 0.9rem;">
                        <li>Confirmation code requirements</li>
                        <li>Dry-run preview mode</li>
                        <li>Environment protection</li>
                        <li>Comprehensive logging</li>
                        <li>Error handling with rollback info</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🗄️ Data Coverage</h3>
                    <p>Handles all user-generated data including contractor system</p>
                    <ul style="margin: 0; padding-left: 20px; color: #6b7280; font-size: 0.9rem;">
                        <li>Projects (active & archived)</li>
                        <li>Chat messages & reactions</li>
                        <li>Customers & contractor companies</li>
                        <li>User records & onboarding data</li>
                        <li>File storage (optional)</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔄 Dependency Management</h3>
                    <p>Respects foreign key relationships for clean deletion</p>
                    <ul style="margin: 0; padding-left: 20px; color: #6b7280; font-size: 0.9rem;">
                        <li>Correct deletion order</li>
                        <li>No orphaned references</li>
                        <li>Schema preservation</li>
                        <li>Index maintenance</li>
                        <li>Constraint integrity</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🛠️ Multiple Interfaces</h3>
                    <p>Various ways to interact with the reset system</p>
                    <ul style="margin: 0; padding-left: 20px; color: #6b7280; font-size: 0.9rem;">
                        <li>Interactive shell script</li>
                        <li>Direct Convex function calls</li>
                        <li>Dry-run analysis mode</li>
                        <li>Legacy compatibility</li>
                        <li>Batch operations</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Deletion Order -->
        <div class="test-section">
            <h2>🔄 Deletion Order (Respects Dependencies)</h2>
            <div class="deletion-order">
                <div class="deletion-step">
                    <div class="deletion-step-number">1</div>
                    <div class="deletion-step-content">
                        <h4>Typing Indicators</h4>
                        <p>Real-time typing status (no dependencies)</p>
                    </div>
                </div>
                
                <div class="deletion-step">
                    <div class="deletion-step-number">2</div>
                    <div class="deletion-step-content">
                        <h4>Chat Messages & Reactions</h4>
                        <p>All chat data including emoji reactions (depends on logEntries)</p>
                    </div>
                </div>
                
                <div class="deletion-step">
                    <div class="deletion-step-number">3</div>
                    <div class="deletion-step-content">
                        <h4>Image Likes & Customer Sessions</h4>
                        <p>Customer interaction data (depends on logEntries and projects)</p>
                    </div>
                </div>
                
                <div class="deletion-step">
                    <div class="deletion-step-number">4</div>
                    <div class="deletion-step-content">
                        <h4>Log Entries & Associated Images</h4>
                        <p>Project log entries and their images (depends on projects)</p>
                    </div>
                </div>
                
                <div class="deletion-step">
                    <div class="deletion-step-number">5</div>
                    <div class="deletion-step-content">
                        <h4>Projects & Job Data</h4>
                        <p>All projects including archived ones and job photos (depends on customers)</p>
                    </div>
                </div>
                
                <div class="deletion-step">
                    <div class="deletion-step-number">6</div>
                    <div class="deletion-step-content">
                        <h4>Customers & Contractor Companies</h4>
                        <p>Both regular customers and contractor companies (no dependencies after projects)</p>
                    </div>
                </div>
                
                <div class="deletion-step">
                    <div class="deletion-step-number">7</div>
                    <div class="deletion-step-content">
                        <h4>User Records</h4>
                        <p>Contractor onboarding data and user management (no dependencies after customers)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Examples -->
        <div class="test-section">
            <h2>🛠️ Usage Examples</h2>
            
            <div class="usage-examples">
                <h3>🔍 Safe Analysis Commands</h3>
                
                <div class="example-item">
                    <h4>Check Database State</h4>
                    <p>Get comprehensive overview of current data</p>
                    <div class="command-block">
npx convex run clearAllProjectData:getProjectDataCount '{}'
                    </div>
                </div>
                
                <div class="example-item">
                    <h4>Dry-Run Analysis</h4>
                    <p>Preview what would be deleted without actually deleting</p>
                    <div class="command-block">
<span class="comment"># Database records only</span>
npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": false}'

<span class="comment"># Include file storage analysis</span>
npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": true}'
                    </div>
                </div>
            </div>
            
            <div class="usage-examples" style="background: #fef2f2; border-color: #fecaca;">
                <h3 style="color: #dc2626;">⚠️ Destructive Operations</h3>
                
                <div class="example-item">
                    <h4>Interactive Shell Script (Recommended)</h4>
                    <p>Safest method with built-in confirmations and progress reporting</p>
                    <div class="command-block">
./scripts/reset-database.sh
<span class="comment"># Follow the interactive menu</span>
                    </div>
                </div>
                
                <div class="example-item">
                    <h4>Comprehensive Reset (Database Only)</h4>
                    <p>Delete all user data but preserve file storage</p>
                    <div class="command-block">
npx convex run clearAllProjectData:comprehensiveReset '{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA",
  "dryRun": false,
  "includeFileStorage": false
}'
                    </div>
                </div>
                
                <div class="example-item">
                    <h4>Complete Wipe (Database + Files)</h4>
                    <p>Delete all user data including images and attachments</p>
                    <div class="command-block">
npx convex run clearAllProjectData:comprehensiveReset '{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA",
  "dryRun": false,
  "includeFileStorage": true
}'
                    </div>
                </div>
            </div>
        </div>

        <!-- Safety Checklist -->
        <div class="safety-checklist">
            <h3>✅ Safety Checklist</h3>
            <ul>
                <li><strong>Environment Check:</strong> Confirm you're in development/testing environment</li>
                <li><strong>Backup Verification:</strong> Ensure any important data is backed up</li>
                <li><strong>Team Communication:</strong> Notify team members before shared environment resets</li>
                <li><strong>Dry-Run First:</strong> Always run dry-run analysis before actual deletion</li>
                <li><strong>Confirmation Code:</strong> Use exact code: <span class="highlight">DELETE_ALL_PROJECT_DATA</span></li>
                <li><strong>Result Verification:</strong> Check final state after operations</li>
                <li><strong>Test Data Creation:</strong> Recreate consistent test data after reset</li>
            </ul>
        </div>

        <!-- Interactive Script Features -->
        <div class="test-section">
            <h2>🖥️ Interactive Script Features</h2>
            <p>The shell script provides the safest and most user-friendly interface:</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📊 Analysis Options</h3>
                    <div class="command-block">
<span class="comment"># Option 1: Database state</span>
Check comprehensive database state

<span class="comment"># Option 2: Preview mode</span>
Dry-run analysis (preview deletions)
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🗑️ Reset Options</h3>
                    <div class="command-block">
<span class="comment"># Option 3: Recommended</span>
Comprehensive database reset

<span class="comment"># Option 4: Backward compatibility</span>
Legacy project data clear
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🧪 Testing Options</h3>
                    <div class="command-block">
<span class="comment"># Option 5: Sample data</span>
Create test data

<span class="comment"># Option 6: Complete refresh</span>
Full reset + create test data
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🔒 Safety Features</h3>
                    <ul style="margin: 0; padding-left: 20px; color: #6b7280; font-size: 0.9rem;">
                        <li>Confirmation prompts</li>
                        <li>Progress reporting</li>
                        <li>Error handling</li>
                        <li>Before/after state comparison</li>
                        <li>Operation summaries</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Documentation -->
        <div class="test-section">
            <h2>📚 Documentation</h2>
            <p>Comprehensive documentation is available in the project:</p>
            
            <div class="example-item">
                <h4>Complete Guide</h4>
                <p>Detailed documentation with examples, troubleshooting, and best practices</p>
                <div class="command-block">
docs/DATABASE_RESET_GUIDE.md
                </div>
            </div>
            
            <div class="example-item">
                <h4>Interactive Script</h4>
                <p>Enhanced shell script with safety features and user-friendly interface</p>
                <div class="command-block">
scripts/reset-database.sh
                </div>
            </div>
            
            <div class="example-item">
                <h4>Convex Functions</h4>
                <p>Backend functions with comprehensive logging and error handling</p>
                <div class="command-block">
convex/clearAllProjectData.ts
                </div>
            </div>
        </div>

        <!-- Final Warning -->
        <div class="warning-banner">
            <h2>🚨 FINAL REMINDER</h2>
            <p>Always use dry-run mode first and confirm you're in a development environment!</p>
        </div>
    </div>
</body>
</html>
