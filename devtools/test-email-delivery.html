<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email Delivery - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2563eb;
            margin-bottom: 20px;
        }
        .test-section {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .success-box {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .error-box {
            background: #fee2e2;
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        button.secondary {
            background: #6b7280;
        }
        button.secondary:hover {
            background: #4b5563;
        }
        button.danger {
            background: #dc2626;
        }
        button.danger:hover {
            background: #b91c1c;
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .loading {
            display: none;
            color: #6b7280;
            text-align: center;
            padding: 20px;
        }
        .loading.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Test Email Delivery System</h1>
        
        <div class="info-box">
            <strong>Hva tester vi?</strong><br>
            Denne siden tester email delivery systemet for team invitasjoner og identifiserer 
            potensielle problemer med Resend integration, API keys, og email templates.
        </div>

        <div class="warning-box">
            <strong>⚠️ Viktig:</strong><br>
            • Åpne Developer Tools (F12) og gå til Console tab<br>
            • Se etter feilmeldinger under email sending<br>
            • Sjekk spam/junk folder for test emails<br>
            • Test med både administrator og utførende roller
        </div>

        <div class="test-section">
            <h3>🔍 Test Email Sending</h3>
            <form id="emailTestForm">
                <div class="form-group">
                    <label for="testEmail">Test Email Address:</label>
                    <input type="email" id="testEmail" placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="testFirstName">First Name:</label>
                    <input type="text" id="testFirstName" placeholder="Test" required>
                </div>
                
                <div class="form-group">
                    <label for="testLastName">Last Name:</label>
                    <input type="text" id="testLastName" placeholder="User" required>
                </div>
                
                <div class="form-group">
                    <label for="testPhone">Phone (8 digits):</label>
                    <input type="text" id="testPhone" placeholder="12345678" pattern="[0-9]{8}" required>
                </div>
                
                <div class="form-group">
                    <label for="testRole">Role:</label>
                    <select id="testRole" required>
                        <option value="administrator">Administrator</option>
                        <option value="utfoerende">Utførende</option>
                    </select>
                </div>
                
                <button type="submit">Send Test Invitation</button>
                <button type="button" onclick="testResendDirectly()" class="secondary">Test Resend API Directly</button>
            </form>
            
            <div class="loading" id="loading">
                ⏳ Sending email...
            </div>
            
            <div id="testResult"></div>
        </div>

        <div class="test-section">
            <h3>🔧 Debug Information</h3>
            <button onclick="checkEmailConfig()">Check Email Configuration</button>
            <button onclick="checkConvexLogs()" class="secondary">Check Recent Logs</button>
            <button onclick="testEmailTemplate()" class="secondary">Test Email Template</button>
            <div id="debugInfo"></div>
        </div>

        <div class="test-section">
            <h3>📋 Console Output</h3>
            <button onclick="clearConsole()" class="secondary">Clear Console</button>
            <div id="console" class="console-output">Console output will appear here...</div>
        </div>

        <div class="test-section">
            <h3>🚨 Common Issues & Solutions</h3>
            <div class="info-box">
                <strong>Potential Issues:</strong><br>
                1. <strong>Resend API Key:</strong> Invalid or expired API key<br>
                2. <strong>From Domain:</strong> Using unverified domain (<EMAIL>)<br>
                3. <strong>Rate Limits:</strong> Resend free tier limitations<br>
                4. <strong>Email Content:</strong> HTML/text template issues<br>
                5. <strong>Spam Filters:</strong> Emails going to spam/junk<br>
                6. <strong>Role Validation:</strong> Issues with "utfoerende" vs "utførende"
            </div>
        </div>

        <div class="info-box" style="margin-top: 30px;">
            <strong>💡 Debugging Tips:</strong><br>
            1. Check browser console for JavaScript errors<br>
            2. Look for Convex action errors in logs<br>
            3. Verify Resend API key is valid<br>
            4. Test with different email providers (Gmail, Outlook, etc.)<br>
            5. Check if emails are in spam/junk folder<br>
            6. Verify role parameter matches expected values
        </div>
    </div>

    <script>
        let convexClient = null;
        let currentUser = null;

        // Console capture
        const consoleElement = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString('no-NO');
            const colors = {
                log: '#10b981',
                error: '#dc2626', 
                warn: '#f59e0b',
                info: '#3b82f6'
            };
            const color = colors[type] || colors.log;
            
            consoleElement.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            consoleElement.scrollTop = consoleElement.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // Initialize Convex client
        async function initConvex() {
            try {
                const { ConvexHttpClient } = await import('https://cdn.skypack.dev/convex/browser');
                convexClient = new ConvexHttpClient('https://standing-aardvark-575.convex.cloud');
                console.log('✅ Convex client initialized');
                return true;
            } catch (error) {
                console.error('❌ Failed to initialize Convex:', error);
                return false;
            }
        }

        // Check current user
        async function checkCurrentUser() {
            if (window.Clerk && window.Clerk.user) {
                currentUser = window.Clerk.user;
                console.log('👤 Current user:', currentUser.id, currentUser.emailAddresses[0]?.emailAddress);
                return true;
            } else {
                console.error('❌ No user logged in');
                return false;
            }
        }

        // Handle form submission
        document.getElementById('emailTestForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const loading = document.getElementById('loading');
            const result = document.getElementById('testResult');
            
            loading.classList.add('show');
            result.innerHTML = '';
            
            try {
                if (!currentUser) {
                    const userCheck = await checkCurrentUser();
                    if (!userCheck) {
                        throw new Error('You must be logged in to send invitations');
                    }
                }
                
                const formData = {
                    email: document.getElementById('testEmail').value,
                    firstName: document.getElementById('testFirstName').value,
                    lastName: document.getElementById('testLastName').value,
                    phone: document.getElementById('testPhone').value,
                    role: document.getElementById('testRole').value,
                    invitedBy: currentUser.id
                };
                
                console.log('📧 Sending test invitation with data:', formData);
                
                const invitationResult = await convexClient.action('teamManagement:inviteTeamMemberMagicLink', formData);
                
                console.log('✅ Invitation result:', invitationResult);
                
                result.innerHTML = `
                    <div class="${invitationResult.emailSent ? 'success' : 'error'}-box">
                        <strong>Result:</strong><br>
                        ${invitationResult.message}<br><br>
                        <strong>Email Sent:</strong> ${invitationResult.emailSent ? 'Yes' : 'No'}<br>
                        ${invitationResult.emailId ? `<strong>Email ID:</strong> ${invitationResult.emailId}<br>` : ''}
                        <strong>Invitation Link:</strong> <a href="${invitationResult.invitationLink}" target="_blank">Test Link</a>
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Test invitation failed:', error);
                result.innerHTML = `
                    <div class="error-box">
                        <strong>Error:</strong><br>
                        ${error.message}
                    </div>
                `;
            } finally {
                loading.classList.remove('show');
            }
        });

        async function testResendDirectly() {
            console.log('🧪 Testing Resend API directly...');
            
            try {
                const testResult = await convexClient.action('emails:sendNotificationEmail', {
                    to: document.getElementById('testEmail').value || '<EMAIL>',
                    subject: 'Test Email from JobbLogg',
                    html: '<h1>Test Email</h1><p>This is a test email to verify Resend integration.</p>',
                    text: 'Test Email - This is a test email to verify Resend integration.',
                    tags: [{ name: 'type', value: 'test' }]
                });
                
                console.log('✅ Direct Resend test result:', testResult);
                
                document.getElementById('testResult').innerHTML = `
                    <div class="${testResult.success ? 'success' : 'error'}-box">
                        <strong>Direct Resend Test:</strong><br>
                        ${testResult.message}<br>
                        ${testResult.emailId ? `Email ID: ${testResult.emailId}` : ''}
                        ${testResult.error ? `Error: ${testResult.error}` : ''}
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Direct Resend test failed:', error);
            }
        }

        function checkEmailConfig() {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <div class="info-box">
                    <strong>Email Configuration:</strong><br>
                    • API Key: re_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL (hardcoded)<br>
                    • From Address: <EMAIL><br>
                    • Service: Resend<br>
                    • Template: Magic Link Invitation<br><br>
                    
                    <strong>Potential Issues:</strong><br>
                    • Using Resend's test domain (may have limitations)<br>
                    • API key should be in environment variables<br>
                    • Role validation: "utfoerende" vs "utførende"
                </div>
            `;
        }

        function checkConvexLogs() {
            console.log('📋 Check Convex logs in terminal: npx convex logs --tail');
            document.getElementById('debugInfo').innerHTML = `
                <div class="warning-box">
                    <strong>Check Convex Logs:</strong><br>
                    Run in terminal: <code>npx convex logs --tail</code><br>
                    Look for email sending errors and API responses.
                </div>
            `;
        }

        function testEmailTemplate() {
            console.log('📧 Testing email template generation...');
            // This would test the email template without sending
            document.getElementById('debugInfo').innerHTML = `
                <div class="info-box">
                    <strong>Email Template Test:</strong><br>
                    Template includes proper Norwegian text, magic link button, and role-specific content.<br>
                    Check console for template generation details.
                </div>
            `;
        }

        function clearConsole() {
            consoleElement.innerHTML = 'Console cleared...\n';
        }

        // Initialize when page loads
        window.addEventListener('load', async () => {
            console.log('📧 Email delivery test page loaded');
            await initConvex();
            await checkCurrentUser();
        });
    </script>
</body>
</html>
