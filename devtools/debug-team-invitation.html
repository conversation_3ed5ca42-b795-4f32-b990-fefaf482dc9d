<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Team Invitation Flow - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2563eb;
            margin-bottom: 20px;
        }
        .debug-section {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .success-box {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .error-box {
            background: #fee2e2;
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        button.secondary {
            background: #6b7280;
        }
        button.secondary:hover {
            background: #4b5563;
        }
        button.danger {
            background: #dc2626;
        }
        button.danger:hover {
            background: #b91c1c;
        }
        .debug-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
        }
        .status-card h4 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }
        .status-value {
            font-weight: 600;
        }
        .status-value.true {
            color: #10b981;
        }
        .status-value.false {
            color: #dc2626;
        }
        .status-value.null {
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Team Invitation Flow</h1>
        
        <div class="info-box">
            <strong>Purpose:</strong><br>
            This tool helps debug why team members are being redirected to contractor onboarding 
            instead of the main dashboard after accepting invitations.
        </div>

        <div class="warning-box">
            <strong>⚠️ Important:</strong><br>
            • You must be logged in as the user experiencing the issue<br>
            • Open Developer Tools (F12) to see detailed console logs<br>
            • This tool checks both database state and localStorage
        </div>

        <div class="debug-section">
            <h3>🔍 Current User Status</h3>
            <button onclick="checkUserStatus()">Check User Status</button>
            <button onclick="checkOnboardingStatus()">Check Onboarding Status</button>
            <button onclick="checkLocalStorage()">Check localStorage</button>
            <div id="userStatus"></div>
        </div>

        <div class="debug-section">
            <h3>🛠️ Fix Team Member Issues</h3>
            <button onclick="fixTeamMemberFlags()">Fix Team Member Flags</button>
            <button onclick="clearAllFlags()" class="secondary">Clear All Flags</button>
            <button onclick="simulateTeamMember()" class="secondary">Simulate Team Member</button>
            <button onclick="repairDatabaseRecord()" class="danger">🔧 Repair Database Record</button>
            <button onclick="createUserManually()" class="danger">👤 Create User Manually</button>
            <div id="fixResults"></div>
        </div>

        <div class="debug-section">
            <h3>📊 Detailed Status</h3>
            <div class="status-grid" id="statusGrid">
                <!-- Status cards will be populated here -->
            </div>
        </div>

        <div class="debug-section">
            <h3>🧪 Test Navigation</h3>
            <button onclick="testMainNavigation()">Test Navigate to Main (/)</button>
            <button onclick="testOnboardingNavigation()" class="secondary">Test Navigate to Onboarding</button>
            <div id="navigationResults"></div>
        </div>

        <div class="debug-section">
            <h3>📋 Debug Output</h3>
            <button onclick="clearDebugOutput()" class="secondary">Clear Output</button>
            <div id="debugOutput" class="debug-output">Debug output will appear here...</div>
        </div>

        <div class="info-box" style="margin-top: 30px;">
            <strong>💡 Common Issues & Solutions:</strong><br>
            1. <strong>Database not synced:</strong> Wait 2-3 seconds after invitation acceptance<br>
            2. <strong>localStorage missing:</strong> Use "Fix Team Member Flags" button<br>
            3. <strong>Timing issues:</strong> Refresh page and check status again<br>
            4. <strong>Onboarding guard logic:</strong> Check console for guard decision logs
        </div>
    </div>

    <script>
        let currentUser = null;
        let convexClient = null;

        // Debug output capture
        const debugElement = document.getElementById('debugOutput');
        
        function addToDebug(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('no-NO');
            const colors = {
                info: '#3b82f6',
                success: '#10b981',
                error: '#dc2626',
                warning: '#f59e0b'
            };
            const color = colors[type] || colors.info;
            
            debugElement.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            debugElement.scrollTop = debugElement.scrollHeight;
        }

        // Initialize Convex client
        async function initConvex() {
            try {
                const { ConvexHttpClient } = await import('https://cdn.skypack.dev/convex/browser');
                convexClient = new ConvexHttpClient('https://standing-aardvark-575.convex.cloud');
                addToDebug('✅ Convex client initialized', 'success');
                return true;
            } catch (error) {
                addToDebug('❌ Failed to initialize Convex: ' + error.message, 'error');
                return false;
            }
        }

        async function checkUserStatus() {
            addToDebug('🔍 Checking current user status...');
            
            try {
                if (window.Clerk && window.Clerk.user) {
                    currentUser = window.Clerk.user;
                    const userInfo = {
                        id: currentUser.id,
                        email: currentUser.emailAddresses[0]?.emailAddress,
                        firstName: currentUser.firstName,
                        lastName: currentUser.lastName
                    };
                    
                    addToDebug('👤 Current user: ' + JSON.stringify(userInfo, null, 2), 'success');
                    
                    document.getElementById('userStatus').innerHTML = `
                        <div class="success-box">
                            <strong>User Logged In:</strong><br>
                            ID: ${userInfo.id}<br>
                            Email: ${userInfo.email}<br>
                            Name: ${userInfo.firstName} ${userInfo.lastName}
                        </div>
                    `;
                    
                    return true;
                } else {
                    addToDebug('❌ No user logged in', 'error');
                    document.getElementById('userStatus').innerHTML = `
                        <div class="error-box">No user logged in. Please log in first.</div>
                    `;
                    return false;
                }
            } catch (error) {
                addToDebug('❌ Error checking user status: ' + error.message, 'error');
                return false;
            }
        }

        async function checkOnboardingStatus() {
            if (!currentUser) {
                const userCheck = await checkUserStatus();
                if (!userCheck) return;
            }

            addToDebug('🔍 Checking onboarding status from database...');
            
            try {
                const status = await convexClient.query('contractorOnboardingSafe:getContractorOnboardingStatusSafe', {
                    clerkUserId: currentUser.id
                });
                
                addToDebug('📊 Database onboarding status: ' + JSON.stringify(status, null, 2), 'info');
                
                updateStatusGrid('database', status);
                
            } catch (error) {
                addToDebug('❌ Error checking onboarding status: ' + error.message, 'error');
            }
        }

        function checkLocalStorage() {
            if (!currentUser) {
                addToDebug('❌ No user logged in for localStorage check', 'error');
                return;
            }

            addToDebug('🔍 Checking localStorage flags...');
            
            const userId = currentUser.id;
            const contractorKey = `jobblogg-contractor-completed-${userId}`;
            const teamMemberKey = `jobblogg-team-member-${userId}`;
            
            const contractorCompleted = localStorage.getItem(contractorKey);
            const isTeamMember = localStorage.getItem(teamMemberKey);
            
            const localStorageStatus = {
                contractorCompleted: contractorCompleted === 'true',
                isTeamMember: isTeamMember === 'true',
                contractorKey,
                teamMemberKey,
                contractorValue: contractorCompleted,
                teamMemberValue: isTeamMember
            };
            
            addToDebug('💾 localStorage status: ' + JSON.stringify(localStorageStatus, null, 2), 'info');
            
            updateStatusGrid('localStorage', localStorageStatus);
        }

        function updateStatusGrid(type, data) {
            const grid = document.getElementById('statusGrid');
            
            let cardHtml = `
                <div class="status-card">
                    <h4>${type === 'database' ? '🗄️ Database Status' : '💾 localStorage Status'}</h4>
            `;
            
            if (type === 'database') {
                cardHtml += `
                    <div class="status-item">
                        <span>Contractor Completed:</span>
                        <span class="status-value ${data.contractorCompleted}">${data.contractorCompleted}</span>
                    </div>
                    <div class="status-item">
                        <span>Has Company ID:</span>
                        <span class="status-value ${!!data.contractorCompanyId}">${!!data.contractorCompanyId}</span>
                    </div>
                    <div class="status-item">
                        <span>User Exists:</span>
                        <span class="status-value ${data.exists}">${data.exists}</span>
                    </div>
                    <div class="status-item">
                        <span>Auth Error:</span>
                        <span class="status-value ${!!data.authError}">${!!data.authError}</span>
                    </div>
                `;
            } else {
                cardHtml += `
                    <div class="status-item">
                        <span>Contractor Completed:</span>
                        <span class="status-value ${data.contractorCompleted}">${data.contractorCompleted}</span>
                    </div>
                    <div class="status-item">
                        <span>Is Team Member:</span>
                        <span class="status-value ${data.isTeamMember}">${data.isTeamMember}</span>
                    </div>
                    <div class="status-item">
                        <span>Contractor Key:</span>
                        <span class="status-value">${data.contractorValue || 'null'}</span>
                    </div>
                    <div class="status-item">
                        <span>Team Member Key:</span>
                        <span class="status-value">${data.teamMemberValue || 'null'}</span>
                    </div>
                `;
            }
            
            cardHtml += '</div>';
            
            // Replace existing card or add new one
            const existingCard = grid.querySelector(`[data-type="${type}"]`);
            if (existingCard) {
                existingCard.outerHTML = `<div data-type="${type}">${cardHtml}</div>`;
            } else {
                grid.innerHTML += `<div data-type="${type}">${cardHtml}</div>`;
            }
        }

        function fixTeamMemberFlags() {
            if (!currentUser) {
                addToDebug('❌ No user logged in', 'error');
                return;
            }

            addToDebug('🔧 Fixing team member flags...', 'warning');
            
            const userId = currentUser.id;
            const contractorKey = `jobblogg-contractor-completed-${userId}`;
            const teamMemberKey = `jobblogg-team-member-${userId}`;
            
            localStorage.setItem(contractorKey, 'true');
            localStorage.setItem(teamMemberKey, 'true');
            
            addToDebug('✅ Team member flags set successfully', 'success');
            
            document.getElementById('fixResults').innerHTML = `
                <div class="success-box">
                    <strong>Flags Fixed:</strong><br>
                    • Contractor completed: true<br>
                    • Team member: true<br>
                    Try navigating to main page now.
                </div>
            `;
            
            // Refresh localStorage check
            checkLocalStorage();
        }

        function clearAllFlags() {
            if (!currentUser) {
                addToDebug('❌ No user logged in', 'error');
                return;
            }

            addToDebug('🧹 Clearing all localStorage flags...', 'warning');
            
            const userId = currentUser.id;
            const contractorKey = `jobblogg-contractor-completed-${userId}`;
            const teamMemberKey = `jobblogg-team-member-${userId}`;
            
            localStorage.removeItem(contractorKey);
            localStorage.removeItem(teamMemberKey);
            
            addToDebug('✅ All flags cleared', 'success');
            
            document.getElementById('fixResults').innerHTML = `
                <div class="warning-box">
                    <strong>Flags Cleared:</strong><br>
                    All localStorage flags have been removed. 
                    You may be redirected to onboarding now.
                </div>
            `;
            
            // Refresh localStorage check
            checkLocalStorage();
        }

        function simulateTeamMember() {
            fixTeamMemberFlags();
            addToDebug('🎭 Simulated team member state', 'info');
        }

        async function repairDatabaseRecord() {
            if (!currentUser) {
                addToDebug('❌ No user logged in', 'error');
                return;
            }

            addToDebug('🔧 Attempting to repair database record...', 'warning');

            try {
                const email = currentUser.emailAddresses[0]?.emailAddress;
                const clerkUserId = currentUser.id;

                addToDebug(`📧 Email: ${email}`, 'info');
                addToDebug(`🆔 Clerk ID: ${clerkUserId}`, 'info');

                if (!email) {
                    addToDebug('❌ No email address found', 'error');
                    return;
                }

                // Call the repair function
                const result = await convexClient.mutation('teamManagement:repairTeamMemberRecord', {
                    email: email,
                    clerkUserId: clerkUserId
                });

                addToDebug('✅ Database record repaired successfully!', 'success');
                addToDebug('📊 Repair result: ' + JSON.stringify(result, null, 2), 'success');

                document.getElementById('fixResults').innerHTML = `
                    <div class="success-box">
                        <strong>Database Record Repaired:</strong><br>
                        • Action: ${result.action}<br>
                        • Company ID: ${result.contractorCompanyId}<br>
                        • Role: ${result.role}<br>
                        <br>
                        <strong>Next Steps:</strong><br>
                        1. Refresh the page<br>
                        2. Try navigating to main dashboard<br>
                        3. Check if company association works
                    </div>
                `;

                // Refresh status checks
                setTimeout(() => {
                    checkOnboardingStatus();
                    checkLocalStorage();
                }, 1000);

            } catch (error) {
                addToDebug('❌ Error repairing database record: ' + error.message, 'error');

                document.getElementById('fixResults').innerHTML = `
                    <div class="error-box">
                        <strong>Repair Failed:</strong><br>
                        ${error.message}<br>
                        <br>
                        Check console for more details.
                    </div>
                `;
            }
        }

        async function createUserManually() {
            if (!currentUser) {
                addToDebug('❌ No user logged in', 'error');
                return;
            }

            addToDebug('👤 Creating user record manually...', 'warning');

            try {
                const email = currentUser.emailAddresses[0]?.emailAddress;
                const clerkUserId = currentUser.id;

                addToDebug(`📧 Email: ${email}`, 'info');
                addToDebug(`🆔 Clerk ID: ${clerkUserId}`, 'info');

                if (!email) {
                    addToDebug('❌ No email address found', 'error');
                    return;
                }

                // Call the manual user creation function
                const result = await convexClient.mutation('teamManagement:createUserManually', {
                    clerkUserId: clerkUserId,
                    email: email
                });

                addToDebug('✅ User record created successfully!', 'success');
                addToDebug('📊 Creation result: ' + JSON.stringify(result, null, 2), 'success');

                document.getElementById('fixResults').innerHTML = `
                    <div class="success-box">
                        <strong>User Record Created:</strong><br>
                        • Action: ${result.action}<br>
                        • User ID: ${result.user._id}<br>
                        • Clerk ID: ${result.user.clerkUserId}<br>
                        ${result.contractorCompanyId ? `• Company ID: ${result.contractorCompanyId}<br>` : ''}
                        ${result.role ? `• Role: ${result.role}<br>` : ''}
                        <br>
                        <strong>Next Steps:</strong><br>
                        1. Refresh the page<br>
                        2. Try navigating to main dashboard<br>
                        3. Check if company association works
                    </div>
                `;

                // Refresh status checks
                setTimeout(() => {
                    checkOnboardingStatus();
                    checkLocalStorage();
                }, 1000);

            } catch (error) {
                addToDebug('❌ Error creating user record: ' + error.message, 'error');

                document.getElementById('fixResults').innerHTML = `
                    <div class="error-box">
                        <strong>User Creation Failed:</strong><br>
                        ${error.message}<br>
                        <br>
                        Check console for more details.
                    </div>
                `;
            }
        }

        function testMainNavigation() {
            addToDebug('🧪 Testing navigation to main page...', 'info');
            addToDebug('Watch for onboarding guard logs in browser console', 'warning');
            
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }

        function testOnboardingNavigation() {
            addToDebug('🧪 Testing navigation to onboarding...', 'info');
            window.location.href = '/contractor-onboarding';
        }

        function clearDebugOutput() {
            debugElement.innerHTML = 'Debug output cleared...\n';
        }

        // Initialize when page loads
        window.addEventListener('load', async () => {
            addToDebug('🔧 Team invitation debug tool loaded', 'info');
            await initConvex();
            await checkUserStatus();
        });
    </script>
</body>
</html>
