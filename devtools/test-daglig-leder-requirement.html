<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLog<PERSON> - <PERSON><PERSON><PERSON> Leder Krav</title>
    <style>
        body {
            font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        .header h1 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            color: #6b7280;
            font-size: 1.1rem;
            margin: 0;
        }
        .success-banner {
            background: #f0fdf4;
            border: 2px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .success-banner h2 {
            color: #166534;
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }
        .success-banner p {
            color: #15803d;
            margin: 0;
            font-weight: 500;
        }
        .requirement-box {
            background: #eff6ff;
            border-left: 4px solid #2563eb;
            border-radius: 0 8px 8px 0;
            padding: 20px;
            margin: 20px 0;
        }
        .requirement-box .icon {
            width: 32px;
            height: 32px;
            background: #2563eb;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }
        .requirement-box h3 {
            color: #1e40af;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .requirement-box p {
            color: #1e3a8a;
            margin: 0;
            font-size: 1rem;
        }
        .requirement-box strong {
            color: #1e40af;
            font-weight: 700;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        .feature-card h3 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .feature-card p {
            color: #4b5563;
            margin: 0 0 15px 0;
            font-size: 0.95rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            margin: 15px 0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .test-section h2 {
            color: #1f2937;
            margin: 0 0 15px 0;
            font-size: 1.4rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👨‍💼 Daglig Leder Krav</h1>
            <p>Lagt til krav om daglig leder-rolle i contractor onboarding</p>
        </div>

        <div class="success-banner">
            <h2>✅ KRAV LAGT TIL</h2>
            <p>Informasjon om daglig leder-krav er nå synlig i Step 1 av contractor onboarding</p>
        </div>

        <!-- Requirement Display -->
        <div class="test-section">
            <h2>📋 Ny Informasjonsboks</h2>
            <p>Lagt til en tydelig informasjonsboks i Step1Introduction som forklarer kravet:</p>
            
            <div class="requirement-box">
                <div class="icon">
                    <svg style="width: 16px; height: 16px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h3>Viktig informasjon</h3>
                <p>
                    For å registrere din bedrift i JobbLogg må du være <strong>daglig leder</strong> i selskapet. 
                    Dette sikrer at kun autoriserte personer kan administrere bedriftens prosjekter og kundekommunikasjon.
                </p>
            </div>
        </div>

        <!-- Implementation Details -->
        <div class="test-section">
            <h2>🛠️ Implementeringsdetaljer</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📍 Plassering</h3>
                    <ul class="feature-list">
                        <li>Lagt til i Step1Introduction.tsx</li>
                        <li>Plassert etter velkommen-tekst</li>
                        <li>Før benefits-seksjonen</li>
                        <li>Tydelig visuell fremheving</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🎨 Design</h3>
                    <ul class="feature-list">
                        <li>Blå fargeskjema (jobblogg-primary)</li>
                        <li>Venstre kant-border for oppmerksomhet</li>
                        <li>Info-ikon for visuell identifikasjon</li>
                        <li>Konsistent med JobbLogg design system</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>📝 Innhold</h3>
                    <ul class="feature-list">
                        <li>Tydelig overskrift: "Viktig informasjon"</li>
                        <li>Spesifiserer "daglig leder" krav</li>
                        <li>Forklarer hvorfor kravet eksisterer</li>
                        <li>Profesjonell og informativ tone</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔧 Teknisk</h3>
                    <ul class="feature-list">
                        <li>TypeScript kompatibel</li>
                        <li>Responsive design</li>
                        <li>Tilgjengelig med ARIA-støtte</li>
                        <li>Konsistent med eksisterende kode</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Code Implementation -->
        <div class="test-section">
            <h2>💻 Kode-implementering</h2>
            
            <div class="code-block">
{/* Important Requirement Notice */}
&lt;div className="bg-jobblogg-primary-soft border-l-4 border-jobblogg-primary rounded-r-xl p-6"&gt;
  &lt;div className="flex items-start gap-3"&gt;
    &lt;div className="w-8 h-8 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1"&gt;
      &lt;svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;
        &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /&gt;
      &lt;/svg&gt;
    &lt;/div&gt;
    &lt;div&gt;
      &lt;h4 className="font-semibold text-jobblogg-text-strong mb-2"&gt;
        Viktig informasjon
      &lt;/h4&gt;
      &lt;BodyText className="text-jobblogg-text-muted"&gt;
        For å registrere din bedrift i JobbLogg må du være &lt;strong className="text-jobblogg-text-strong"&gt;daglig leder&lt;/strong&gt; i selskapet. 
        Dette sikrer at kun autoriserte personer kan administrere bedriftens prosjekter og kundekommunikasjon.
      &lt;/BodyText&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;
            </div>
        </div>

        <!-- Rationale -->
        <div class="test-section">
            <h2>🎯 Begrunnelse</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔒 Sikkerhet</h3>
                    <p>Sikrer at kun autoriserte personer kan registrere bedriften og administrere prosjekter.</p>
                </div>
                
                <div class="feature-card">
                    <h3>⚖️ Juridisk ansvar</h3>
                    <p>Daglig leder har juridisk myndighet til å representere selskapet i forretningsforhold.</p>
                </div>
                
                <div class="feature-card">
                    <h3>📋 Compliance</h3>
                    <p>Følger norske forretningspraksis og krav til bedriftsrepresentasjon.</p>
                </div>
                
                <div class="feature-card">
                    <h3>🎯 Klarhet</h3>
                    <p>Tydelig kommunikasjon av krav før brukeren starter registreringsprosessen.</p>
                </div>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="test-section">
            <h2>🧪 Testing</h2>
            
            <div class="feature-card">
                <h3>Slik tester du endringen:</h3>
                <ul class="feature-list">
                    <li>Gå til contractor onboarding (Step 1)</li>
                    <li>Verifiser at informasjonsboksen vises</li>
                    <li>Sjekk at teksten er tydelig og lesbar</li>
                    <li>Bekreft at designet er konsistent</li>
                    <li>Test på mobile enheter for responsivitet</li>
                </ul>
            </div>
        </div>

        <!-- Success Banner -->
        <div class="success-banner">
            <h2>🎉 IMPLEMENTERING FULLFØRT</h2>
            <p>Daglig leder-kravet er nå tydelig kommunisert i contractor onboarding!</p>
        </div>
    </div>
</body>
</html>
