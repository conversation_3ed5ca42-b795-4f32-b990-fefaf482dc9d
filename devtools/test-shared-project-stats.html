<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Shared Project Statistics - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #6b7280;
            margin-bottom: 24px;
        }
        .test-section {
            background: #eff6ff;
            border-left: 4px solid #2563eb;
            padding: 16px;
            margin: 16px 0;
        }
        .test-section h3 {
            margin: 0 0 8px 0;
            color: #1e40af;
        }
        .test-result {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
            font-family: monospace;
            font-size: 13px;
        }
        .test-result.success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }
        .test-result.error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button.secondary {
            background: #6b7280;
        }
        button.secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Test Enhanced Shared Project Statistics</h1>
        <p class="subtitle">Test the new shared project statistics functionality in ProjectDetail page</p>

        <div class="test-section">
            <h3>Test 1: Norwegian Date/Time Formatting</h3>
            <p>Test the formatSharedProjectAccess function with different timestamps.</p>
            <button onclick="testDateFormatting()">Test Date Formatting</button>
            <div id="date-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: View Count Pluralization</h3>
            <p>Test the formatViewCount function with different numbers.</p>
            <button onclick="testViewCount()">Test View Count</button>
            <div id="view-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Project Details Page</h3>
            <p>Navigate to a project details page to see the enhanced statistics.</p>
            <button onclick="openProjectDetails()">Open Project Details</button>
            <button class="secondary" onclick="openDashboard()">Open Dashboard</button>
            <div id="nav-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Mobile Responsiveness</h3>
            <p>Test the responsive grid layout for statistics cards.</p>
            <button onclick="testResponsive()">Test Responsive Layout</button>
            <div id="responsive-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, isSuccess = false, isError = false) {
            const result = document.getElementById(elementId);
            result.textContent = message;
            result.style.display = 'block';
            result.className = 'test-result';
            if (isSuccess) result.className += ' success';
            if (isError) result.className += ' error';
        }

        function testDateFormatting() {
            const now = Date.now();
            const tests = [
                { timestamp: now - 30000, expected: 'Akkurat nå eller X min siden' },
                { timestamp: now - 3600000, expected: '1 time siden' },
                { timestamp: now - 7200000, expected: '2 timer siden' },
                { timestamp: now - 86400000, expected: '1 dag siden' },
                { timestamp: now - 172800000, expected: '2 dager siden' },
                { timestamp: now - 604800000, expected: 'DD.MM.YYYY HH:MM format' },
                { timestamp: null, expected: 'Aldri' }
            ];

            let results = '✅ Norwegian Date/Time Formatting Tests:\n\n';
            tests.forEach((test, index) => {
                const timestamp = test.timestamp;
                const expected = test.expected;
                results += `Test ${index + 1}: ${timestamp ? new Date(timestamp).toISOString() : 'null'}\n`;
                results += `Expected: ${expected}\n\n`;
            });

            results += 'Note: Actual formatting is implemented in the React component.\nThese tests show the expected behavior patterns.';
            showResult('date-result', results, true);
        }

        function testViewCount() {
            const tests = [
                { count: 0, expected: '0 visninger' },
                { count: 1, expected: '1 visning' },
                { count: 2, expected: '2 visninger' },
                { count: 15, expected: '15 visninger' },
                { count: 100, expected: '100 visninger' }
            ];

            let results = '✅ View Count Pluralization Tests:\n\n';
            tests.forEach((test, index) => {
                results += `Count: ${test.count} → Expected: "${test.expected}"\n`;
            });

            results += '\nNote: Actual formatting is implemented in the React component.\nThese tests show the expected Norwegian pluralization.';
            showResult('view-result', results, true);
        }

        function openProjectDetails() {
            window.open('http://localhost:5173', '_blank');
            showResult('nav-result', 
                '✅ Opened JobbLogg dashboard.\n\n' +
                'To test the enhanced statistics:\n' +
                '1. Navigate to any project details page\n' +
                '2. Look for the "Statistikk" section\n' +
                '3. You should see 2-3 StatsCard components:\n' +
                '   - "Totalt bilder" (primary blue)\n' +
                '   - "Siste aktivitet" (accent)\n' +
                '   - "Delt prosjekt" (warning, only if shared)\n\n' +
                '4. The layout should be responsive:\n' +
                '   - Mobile: 1 column\n' +
                '   - Tablet: 2 columns\n' +
                '   - Desktop: 3 columns (when shared stats present)', 
                true
            );
        }

        function openDashboard() {
            window.open('http://localhost:5173', '_blank');
            showResult('nav-result', '✅ Opened dashboard for navigation to project details.', true);
        }

        function testResponsive() {
            showResult('responsive-result', 
                '📱 Responsive Layout Testing:\n\n' +
                'The statistics section uses a responsive grid:\n\n' +
                '• Mobile (< 640px): grid-cols-1\n' +
                '  - All cards stack vertically\n' +
                '  - Full width for better readability\n\n' +
                '• Tablet (640px+): grid-cols-2\n' +
                '  - Two cards per row\n' +
                '  - Shared stats card wraps to next row\n\n' +
                '• Desktop (1024px+): grid-cols-3\n' +
                '  - All three cards in one row (when shared stats present)\n' +
                '  - Two cards centered (when no shared stats)\n\n' +
                'All cards maintain 44px minimum touch targets for WCAG AA compliance.\n\n' +
                'Test by resizing your browser window or using dev tools device emulation.', 
                true
            );
        }
    </script>
</body>
</html>
