<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Stripe Webhook Endpoint</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .loading { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #1d4ed8; }
        button:disabled { background: #9ca3af; cursor: not-allowed; }
        .endpoint-url {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            word-break: break-all;
            margin: 15px 0;
        }
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Stripe Webhook Endpoint</h1>
        
        <div class="instructions">
            <h3>📋 Instruksjoner for Stripe Dashboard:</h3>
            <div class="step">1. Gå til <strong>Stripe Dashboard → Developers → Webhooks</strong></div>
            <div class="step">2. Klikk <strong>"Add endpoint"</strong></div>
            <div class="step">3. Bruk denne URL-en som endpoint:</div>
            <div class="endpoint-url">https://enchanted-quail-174.convex.site/stripe/webhook</div>
            <div class="step">4. Velg disse events: customer.created, checkout.session.completed, customer.subscription.*, invoice.*, payment_intent.*</div>
            <div class="step">5. Etter opprettelse, kopier "Signing secret" (starter med whsec_)</div>
            <div class="step">6. Oppdater STRIPE_WEBHOOK_SECRET i .env.local</div>
        </div>

        <h3>🧪 Test Endpoints:</h3>
        
        <button onclick="testGeneralEndpoint()">Test General Endpoint</button>
        <button onclick="testWebhookEndpoint()">Test Webhook Endpoint</button>
        
        <div id="results"></div>
        
        <h3>📝 Neste steg:</h3>
        <ul>
            <li>Etter du har satt opp webhook i Stripe Dashboard</li>
            <li>Oppdater STRIPE_WEBHOOK_SECRET i .env.local med den nye verdien</li>
            <li>Test en ekte Stripe-transaksjon for å verifisere webhook-funksjonalitet</li>
        </ul>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');

        function showStatus(message, type) {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            resultsDiv.appendChild(statusDiv);
        }

        async function testGeneralEndpoint() {
            showStatus('🔄 Tester general endpoint...', 'loading');
            
            try {
                const response = await fetch('https://enchanted-quail-174.convex.site/test');
                const text = await response.text();
                
                if (response.ok) {
                    showStatus(`✅ General endpoint fungerer: ${text}`, 'success');
                } else {
                    showStatus(`❌ General endpoint feilet: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ Feil ved testing av general endpoint: ${error.message}`, 'error');
            }
        }

        async function testWebhookEndpoint() {
            showStatus('🔄 Tester webhook endpoint...', 'loading');
            
            try {
                // Test with a simple POST request (this will fail signature verification, but should reach the endpoint)
                const response = await fetch('https://enchanted-quail-174.convex.site/stripe/webhook', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ test: 'data' })
                });
                
                const text = await response.text();
                
                if (response.status === 400 && text.includes('signature')) {
                    showStatus('✅ Webhook endpoint er tilgjengelig (signature verification fungerer)', 'success');
                } else if (response.ok) {
                    showStatus(`✅ Webhook endpoint fungerer: ${text}`, 'success');
                } else {
                    showStatus(`⚠️ Webhook endpoint svarte: ${response.status} - ${text}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ Feil ved testing av webhook endpoint: ${error.message}`, 'error');
            }
        }

        // Auto-test general endpoint on load
        window.onload = () => {
            testGeneralEndpoint();
        };
    </script>
</body>
</html>
