<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Company Search - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .results {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result-item {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
        }
        .result-item:hover {
            background: #f3f4f6;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .company-name {
            font-weight: 600;
            color: #1f2937;
        }
        .company-details {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }
        .error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Company Search</h1>
        <p>Test the real-time company search functionality</p>
        
        <input 
            type="text" 
            id="searchInput" 
            placeholder="Type company name (e.g., Equinor, DNB, Telenor)..."
            autocomplete="off"
        >
        
        <div id="results" class="results" style="display: none;"></div>
        
        <div id="debug" style="margin-top: 20px; font-family: monospace; font-size: 12px; background: #f3f4f6; padding: 10px; border-radius: 4px;">
            <strong>Debug Log:</strong><br>
            <div id="debugLog"></div>
        </div>
    </div>

    <script type="module">
        // Import the search function
        import { searchCompanies } from '/src/services/companyLookup.js';

        const searchInput = document.getElementById('searchInput');
        const resultsDiv = document.getElementById('results');
        const debugLog = document.getElementById('debugLog');
        
        let debounceTimeout;
        
        function addDebugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        async function performSearch(query) {
            addDebugLog(`🔍 Starting search for: "${query}"`);
            
            try {
                resultsDiv.innerHTML = '<div class="loading">🔄 Searching...</div>';
                resultsDiv.style.display = 'block';
                
                const result = await searchCompanies(query, 5);
                
                if (result.success) {
                    addDebugLog(`✅ Found ${result.data.companies.length} companies`);
                    
                    if (result.data.companies.length > 0) {
                        resultsDiv.innerHTML = result.data.companies.map(company => `
                            <div class="result-item">
                                <div class="company-name">${company.name}</div>
                                <div class="company-details">
                                    Org.nr: ${company.organizationNumber}<br>
                                    ${company.managingDirector ? 
                                        `Daglig leder: ${company.managingDirector.fullName}<br>` : 
                                        ''
                                    }
                                    ${(() => {
                                        const address = company.visitingAddress || company.businessAddress;
                                        if (address && (address.street || address.postalCode || address.city)) {
                                            const parts = [];
                                            if (address.street) parts.push(address.street);
                                            if (address.postalCode && address.city) parts.push(`${address.postalCode} ${address.city}`);
                                            return `Adresse: ${parts.join(', ')}`;
                                        }
                                        return 'Ingen adresse registrert';
                                    })()}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        resultsDiv.innerHTML = '<div class="loading">Ingen resultater funnet</div>';
                    }
                } else {
                    addDebugLog(`❌ Search failed: ${result.error.message}`);
                    resultsDiv.innerHTML = `<div class="error">Feil: ${result.error.message}</div>`;
                }
                
            } catch (error) {
                addDebugLog(`💥 Unexpected error: ${error.message}`);
                resultsDiv.innerHTML = `<div class="error">Uventet feil: ${error.message}</div>`;
            }
        }
        
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            addDebugLog(`📝 Input changed: "${query}"`);
            
            // Clear existing timeout
            if (debounceTimeout) {
                clearTimeout(debounceTimeout);
                addDebugLog(`⏰ Cleared existing timeout`);
            }
            
            if (query.length >= 2) {
                addDebugLog(`⏰ Setting timeout for search (500ms)`);
                debounceTimeout = setTimeout(() => {
                    addDebugLog(`🚀 Timeout fired, performing search`);
                    performSearch(query);
                }, 500);
            } else {
                addDebugLog(`📏 Query too short (${query.length} chars), hiding results`);
                resultsDiv.style.display = 'none';
            }
        });
        
        addDebugLog('🚀 Test page loaded and ready');
    </script>
</body>
</html>
