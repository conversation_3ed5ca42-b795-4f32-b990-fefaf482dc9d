<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg - Test Log Entry Comments System</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 30px;
            border-radius: 16px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 8px 8px 8px 0;
            transition: all 0.2s;
        }
        .test-button:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        .test-button.secondary {
            background: #6b7280;
        }
        .test-button.secondary:hover {
            background: #4b5563;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .success { background: #d1fae5; border: 1px solid #10b981; color: #065f46; }
        .error { background: #fee2e2; border: 1px solid #ef4444; color: #991b1b; }
        .info { background: #dbeafe; border: 1px solid #3b82f6; color: #1e40af; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; color: #92400e; }
        h2 { color: #2563eb; margin-top: 0; }
        h3 { color: #4b5563; margin-bottom: 10px; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #10b981; }
        .status-fail { background: #ef4444; }
        .status-pending { background: #f59e0b; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 JobbLogg Log Entry Comments System Test</h1>
        <p>Test suite for verifying the new log-entry-based comment system integration</p>
    </div>

    <div class="test-section">
        <h2>📊 Test Overview</h2>
        <p><strong>Fixed Issues:</strong></p>
        <ul>
            <li>✅ Dashboard unread comments now uses logEntryComments system</li>
            <li>✅ UnreadComments page migrated to logEntryComments system</li>
            <li>✅ Self-reply prevention logic implemented</li>
            <li>✅ Duplicate reply interface elements removed</li>
        </ul>
        
        <h3>🎯 Test Objectives</h3>
        <ol>
            <li>Verify dashboard "Uleste kommentarer" updates when customers post log-entry comments</li>
            <li>Confirm reply logic prevents self-replies</li>
            <li>Ensure single, clean reply interface without duplicates</li>
            <li>Test both contractor and customer views work correctly</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🚀 Quick Test Actions</h2>
        
        <h3>1. Dashboard Integration Test</h3>
        <button class="test-button" onclick="openDashboard()">Open Dashboard</button>
        <button class="test-button secondary" onclick="openSharedProject()">Open Shared Project (Customer View)</button>
        <p><strong>Test Steps:</strong></p>
        <ol>
            <li>Open dashboard and note current "Uleste kommentarer" count</li>
            <li>Open a shared project as customer</li>
            <li>Post a comment on a log entry</li>
            <li>Return to dashboard - count should increase</li>
        </ol>

        <h3>2. Reply Logic Test</h3>
        <button class="test-button" onclick="openProjectLog()">Open Project Log (Contractor View)</button>
        <p><strong>Test Steps:</strong></p>
        <ol>
            <li>Open project log as contractor</li>
            <li>Find your own comment - should NOT see "Svar" button</li>
            <li>Find customer comment - should see "Svar" button</li>
            <li>Verify only one reply interface per comment</li>
        </ol>

        <h3>3. Interface Cleanup Test</h3>
        <button class="test-button" onclick="testReplyInterface()">Test Reply Interface</button>
        <p><strong>Expected Behavior:</strong></p>
        <ul>
            <li>Each comment should have only ONE "Svar" button</li>
            <li>No duplicate "Legg til svar" buttons</li>
            <li>Clean, consistent reply interface</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔍 Manual Verification Checklist</h2>
        
        <div style="display: grid; gap: 10px;">
            <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 10px;">
                <span class="status-indicator status-pending"></span>
                Dashboard "Uleste kommentarer" shows correct count
            </label>
            <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 10px;">
                <span class="status-indicator status-pending"></span>
                New customer log-entry comments update dashboard count
            </label>
            <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 10px;">
                <span class="status-indicator status-pending"></span>
                Contractors cannot reply to their own comments
            </label>
            <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 10px;">
                <span class="status-indicator status-pending"></span>
                Customers cannot reply to their own comments
            </label>
            <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 10px;">
                <span class="status-indicator status-pending"></span>
                Only one "Svar" button per comment (no duplicates)
            </label>
            <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 10px;">
                <span class="status-indicator status-pending"></span>
                No "Legg til svar" button conflicts
            </label>
            <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 10px;">
                <span class="status-indicator status-pending"></span>
                UnreadComments page shows log-entry comments correctly
            </label>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 Test Results</h2>
        <div id="results"></div>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
    </div>

    <script>
        function showResult(message, isError = false, isInfo = false) {
            const results = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isError ? 'error' : isInfo ? 'info' : 'success'}`;
            resultDiv.textContent = message;
            results.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function openDashboard() {
            window.open('http://localhost:5173', '_blank');
            showResult('✅ Opened dashboard. Check "Uleste kommentarer" card for current count.', false, true);
        }

        function openSharedProject() {
            // You'll need to replace this with an actual shared project URL
            showResult('🔄 To test shared project:\n\n1. Go to dashboard\n2. Find a project with shared access\n3. Copy the shared link\n4. Open in incognito/private window\n5. Post a comment on a log entry\n6. Return to dashboard to verify count update', false, true);
        }

        function openProjectLog() {
            window.open('http://localhost:5173', '_blank');
            showResult('✅ Opened dashboard. Navigate to a project log to test reply logic.', false, true);
        }

        function testReplyInterface() {
            showResult(`🔍 Reply Interface Test Checklist:

✅ FIXED ISSUES:
- Removed duplicate "Legg til svar" button from root comment
- Added self-reply prevention logic
- Standardized on single "Svar" button per comment

🎯 EXPECTED BEHAVIOR:
- Each comment shows only ONE "Svar" button
- Users cannot reply to their own comments
- Clean, consistent interface without duplicates

📋 MANUAL VERIFICATION:
1. Open any project log or shared project
2. Look at comment threads
3. Verify only one reply button per comment
4. Try to reply to your own comment (should not see button)
5. Confirm interface is clean and intuitive`, false, true);
        }

        // Auto-run initial status check
        window.onload = function() {
            showResult('🚀 Log Entry Comments System Test Suite Ready\n\nAll backend functions updated:\n- Dashboard uses logEntryComments.getUnreadCount\n- UnreadComments uses logEntryComments queries\n- Self-reply prevention implemented\n- Duplicate interfaces removed\n\nReady for manual testing!', false, true);
        };
    </script>
</body>
</html>
