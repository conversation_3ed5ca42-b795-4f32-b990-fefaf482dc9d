<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug: Team Invitasjoner</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1F2937;
            background: #F3F4F6;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #1D4ED8 0%, #2563EB 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        .content {
            padding: 24px;
        }
        .invitation-card {
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: #F9FAFB;
        }
        .invitation-card.accepted {
            border-color: #10B981;
            background: #F0FDF4;
        }
        .invitation-card.pending {
            border-color: #F59E0B;
            background: #FFFBEB;
        }
        .invitation-card.expired {
            border-color: #EF4444;
            background: #FEF2F2;
        }
        .invitation-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-accepted {
            background: #D1FAE5;
            color: #065F46;
        }
        .status-pending {
            background: #FEF3C7;
            color: #92400E;
        }
        .status-expired {
            background: #FEE2E2;
            color: #991B1B;
        }
        .invitation-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            font-size: 14px;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        .detail-label {
            font-weight: 600;
            color: #6B7280;
        }
        .detail-value {
            color: #1F2937;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        .btn {
            background: #2563EB;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .btn:hover {
            background: #1D4ED8;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6B7280;
        }
        .error {
            background: #FEF2F2;
            border: 2px solid #FECACA;
            color: #DC2626;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Debug: Team Invitasjoner</h1>
            <p>Undersøk alle team-invitasjoner for å finne email-problemet</p>
        </div>
        
        <div class="content">
            <button onclick="loadInvitations()" class="btn">🔄 Last inn invitasjoner</button>
            
            <div id="result">
                <div class="loading">Klikk "Last inn invitasjoner" for å se alle invitasjoner</div>
            </div>
        </div>
    </div>

    <script type="module">
        import { ConvexHttpClient } from "https://esm.sh/convex@1.16.4/browser";

        const convex = new ConvexHttpClient("https://standing-aardvark-575.convex.cloud");

        window.loadInvitations = async function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="loading">Laster invitasjoner...</div>';
            
            try {
                const invitations = await convex.query("teamManagement:debugGetAllInvitations");
                
                if (invitations.length === 0) {
                    resultDiv.innerHTML = '<div class="error">Ingen invitasjoner funnet</div>';
                    return;
                }
                
                let html = `<h3>Funnet ${invitations.length} invitasjon(er):</h3>`;
                
                invitations.forEach((invitation, index) => {
                    const status = invitation.invitationStatus || 'unknown';
                    const statusClass = status === 'accepted' ? 'accepted' : 
                                       status === 'pending' ? 'pending' : 'expired';
                    
                    const invitedDate = invitation.invitedAt ? 
                        new Date(invitation.invitedAt).toLocaleString('nb-NO') : 'Ukjent';
                    const acceptedDate = invitation.acceptedAt ? 
                        new Date(invitation.acceptedAt).toLocaleString('nb-NO') : 'Ikke akseptert';
                    
                    html += `
                        <div class="invitation-card ${statusClass}">
                            <div class="invitation-header">
                                <h4>Invitasjon #${index + 1}</h4>
                                <span class="status-badge status-${status}">${status}</span>
                            </div>
                            <div class="invitation-details">
                                <div class="detail-item">
                                    <span class="detail-label">Clerk User ID:</span>
                                    <span class="detail-value">${invitation.clerkUserId || 'Ikke satt'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Rolle:</span>
                                    <span class="detail-value">${invitation.role || 'Ikke satt'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Invitert av:</span>
                                    <span class="detail-value">${invitation.invitedBy || 'Ikke satt'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Invitert dato:</span>
                                    <span class="detail-value">${invitedDate}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Akseptert dato:</span>
                                    <span class="detail-value">${acceptedDate}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Token:</span>
                                    <span class="detail-value">${invitation.invitationToken ? invitation.invitationToken.substring(0, 8) + '...' : 'Ingen'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Company ID:</span>
                                    <span class="detail-value">${invitation.contractorCompanyId || 'Ikke satt'}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                console.error('Error loading invitations:', error);
                resultDiv.innerHTML = `<div class="error">❌ Feil ved lasting av invitasjoner: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
