<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Shared Project Formatting - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        .test-case {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
        }
        .test-case h3 {
            margin: 0 0 8px 0;
            color: #374151;
            font-size: 16px;
        }
        .input {
            background: #e5e7eb;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
            margin: 4px 0;
        }
        .output {
            background: #dcfce7;
            border: 1px solid #bbf7d0;
            padding: 8px;
            border-radius: 4px;
            font-weight: 500;
            color: #16a34a;
            margin: 4px 0;
        }
        .expected {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            padding: 8px;
            border-radius: 4px;
            color: #1d4ed8;
            margin: 4px 0;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            margin: 8px 8px 8px 0;
        }
        button:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Shared Project Formatting Function</h1>
        <p>Testing the formatSharedProjectSubtitle function with various scenarios</p>

        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="testRecentAccess()">Test Recent Access</button>
        <button onclick="testOlderAccess()">Test Older Access</button>
        <button onclick="testNoAccess()">Test No Access</button>

        <div id="results"></div>
    </div>

    <script>
        // Copy of the formatting function from Dashboard.tsx
        function formatSharedProjectSubtitle(sharedStats) {
            if (!sharedStats.mostRecentAccess) {
                return `${sharedStats.sharedProjectCount} delt${sharedStats.sharedProjectCount !== 1 ? 'e' : ''} prosjekt${sharedStats.sharedProjectCount !== 1 ? 'er' : ''}`;
            }

            const now = new Date();
            const accessDate = new Date(sharedStats.mostRecentAccess);
            const diffInHours = (now.getTime() - accessDate.getTime()) / (1000 * 60 * 60);

            // Format relative time for recent access (within 24 hours)
            if (diffInHours < 24) {
                if (diffInHours < 1) {
                    const diffInMinutes = Math.floor(diffInHours * 60);
                    if (diffInMinutes <= 1) {
                        return 'Sist åpnet: Akkurat nå';
                    }
                    return `Sist åpnet: ${diffInMinutes} min siden`;
                }
                const hours = Math.floor(diffInHours);
                return `Sist åpnet: ${hours} ${hours === 1 ? 'time' : 'timer'} siden`;
            }

            // Format absolute date/time for older access
            const diffInDays = Math.floor(diffInHours / 24);
            if (diffInDays === 1) {
                return `Sist åpnet: I går ${accessDate.toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}`;
            } else if (diffInDays < 7) {
                return `Sist åpnet: ${diffInDays} dager siden`;
            } else {
                return `Sist åpnet: ${accessDate.toLocaleDateString('nb-NO', { 
                    day: 'numeric', 
                    month: 'short',
                    year: accessDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
                })} ${accessDate.toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}`;
            }
        }

        function createTestCase(title, input, expected) {
            const result = formatSharedProjectSubtitle(input);
            const isMatch = result === expected;
            
            return `
                <div class="test-case">
                    <h3>${title} ${isMatch ? '✅' : '❌'}</h3>
                    <div class="input">Input: ${JSON.stringify(input, null, 2)}</div>
                    <div class="output">Result: "${result}"</div>
                    <div class="expected">Expected: "${expected}"</div>
                </div>
            `;
        }

        function testRecentAccess() {
            const now = new Date();
            const results = document.getElementById('results');
            
            // Test cases for recent access
            const testCases = [
                {
                    title: "Just now access",
                    input: {
                        sharedProjectCount: 2,
                        totalViews: 5,
                        mostRecentAccess: now.getTime() - (30 * 1000) // 30 seconds ago
                    },
                    expected: "Sist åpnet: Akkurat nå"
                },
                {
                    title: "5 minutes ago",
                    input: {
                        sharedProjectCount: 1,
                        totalViews: 3,
                        mostRecentAccess: now.getTime() - (5 * 60 * 1000) // 5 minutes ago
                    },
                    expected: "Sist åpnet: 5 min siden"
                },
                {
                    title: "2 hours ago",
                    input: {
                        sharedProjectCount: 3,
                        totalViews: 8,
                        mostRecentAccess: now.getTime() - (2 * 60 * 60 * 1000) // 2 hours ago
                    },
                    expected: "Sist åpnet: 2 timer siden"
                },
                {
                    title: "1 hour ago (singular)",
                    input: {
                        sharedProjectCount: 1,
                        totalViews: 2,
                        mostRecentAccess: now.getTime() - (1 * 60 * 60 * 1000) // 1 hour ago
                    },
                    expected: "Sist åpnet: 1 time siden"
                }
            ];

            let html = '<h2>Recent Access Tests</h2>';
            testCases.forEach(test => {
                html += createTestCase(test.title, test.input, test.expected);
            });
            
            results.innerHTML = html;
        }

        function testOlderAccess() {
            const now = new Date();
            const results = document.getElementById('results');
            
            const yesterday = new Date(now.getTime() - (24 * 60 * 60 * 1000));
            const threeDaysAgo = new Date(now.getTime() - (3 * 24 * 60 * 60 * 1000));
            const oneWeekAgo = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
            
            const testCases = [
                {
                    title: "Yesterday access",
                    input: {
                        sharedProjectCount: 2,
                        totalViews: 4,
                        mostRecentAccess: yesterday.getTime()
                    },
                    expected: `Sist åpnet: I går ${yesterday.toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}`
                },
                {
                    title: "3 days ago",
                    input: {
                        sharedProjectCount: 1,
                        totalViews: 6,
                        mostRecentAccess: threeDaysAgo.getTime()
                    },
                    expected: "Sist åpnet: 3 dager siden"
                },
                {
                    title: "1 week ago",
                    input: {
                        sharedProjectCount: 2,
                        totalViews: 10,
                        mostRecentAccess: oneWeekAgo.getTime()
                    },
                    expected: `Sist åpnet: ${oneWeekAgo.toLocaleDateString('nb-NO', { 
                        day: 'numeric', 
                        month: 'short'
                    })} ${oneWeekAgo.toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}`
                }
            ];

            let html = '<h2>Older Access Tests</h2>';
            testCases.forEach(test => {
                html += createTestCase(test.title, test.input, test.expected);
            });
            
            results.innerHTML = html;
        }

        function testNoAccess() {
            const results = document.getElementById('results');
            
            const testCases = [
                {
                    title: "No access - single project",
                    input: {
                        sharedProjectCount: 1,
                        totalViews: 0,
                        mostRecentAccess: null
                    },
                    expected: "1 delt prosjekt"
                },
                {
                    title: "No access - multiple projects",
                    input: {
                        sharedProjectCount: 3,
                        totalViews: 0,
                        mostRecentAccess: null
                    },
                    expected: "3 delte prosjekter"
                }
            ];

            let html = '<h2>No Access Tests</h2>';
            testCases.forEach(test => {
                html += createTestCase(test.title, test.input, test.expected);
            });
            
            results.innerHTML = html;
        }

        function runAllTests() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Running All Tests...</h2>';
            
            setTimeout(() => testRecentAccess(), 100);
            setTimeout(() => {
                results.innerHTML += document.getElementById('results').innerHTML;
                testOlderAccess();
            }, 200);
            setTimeout(() => {
                results.innerHTML += document.getElementById('results').innerHTML;
                testNoAccess();
            }, 300);
        }

        // Run tests on page load
        window.onload = () => {
            testRecentAccess();
        };
    </script>
</body>
</html>
