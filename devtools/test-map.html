<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { border-color: #10B981; background-color: #F0FDF4; }
        .error { border-color: #EF4444; background-color: #FEF2F2; }
        img {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .url-display {
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <h1>🗺️ Google Maps API Test</h1>
    <p>Denne siden tester Google Maps API direkte uten React/Vite for å isolere problemet.</p>

    <div class="test-section">
        <h2>Test 1: Enkel Static Map</h2>
        <p>Testing med en enkel adresse i Oslo:</p>
        <div id="test1-result">
            <div class="url-display" id="test1-url"></div>
            <img id="test1-img" alt="Test map 1" style="display: none;">
            <div id="test1-status">Laster...</div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: Roadmap vs Hybrid</h2>
        <p>Sammenligning av roadmap og hybrid for samme adresse:</p>
        
        <h3>Roadmap:</h3>
        <div id="test2a-result">
            <div class="url-display" id="test2a-url"></div>
            <img id="test2a-img" alt="Roadmap test" style="display: none;">
            <div id="test2a-status">Laster...</div>
        </div>

        <h3>Hybrid:</h3>
        <div id="test2b-result">
            <div class="url-display" id="test2b-url"></div>
            <img id="test2b-img" alt="Hybrid test" style="display: none;">
            <div id="test2b-status">Laster...</div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: API Key Validation</h2>
        <div id="api-key-info"></div>
    </div>

    <script>
        // API Key (replace with your actual key)
        const API_KEY = 'AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs';
        
        // Test addresses
        const testAddress = 'Karl Johans gate 1, 0154 Oslo, Norge';
        
        function createMapUrl(address, mapType = 'roadmap', zoom = 15) {
            const params = new URLSearchParams({
                center: address,
                zoom: zoom.toString(),
                size: '400x300',
                maptype: mapType,
                markers: `color:red|${address}`,
                key: API_KEY
            });
            return `https://maps.googleapis.com/maps/api/staticmap?${params.toString()}`;
        }

        function testImage(imgId, urlId, statusId, url, testName) {
            const img = document.getElementById(imgId);
            const urlDiv = document.getElementById(urlId);
            const statusDiv = document.getElementById(statusId);
            
            urlDiv.textContent = url;
            
            img.onload = function() {
                img.style.display = 'block';
                statusDiv.innerHTML = '<span style="color: #10B981;">✅ Success!</span>';
                statusDiv.parentElement.parentElement.className += ' success';
            };
            
            img.onerror = function() {
                statusDiv.innerHTML = '<span style="color: #EF4444;">❌ Failed to load</span>';
                statusDiv.parentElement.parentElement.className += ' error';
                
                // Try to get more error info
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            return response.text().then(text => {
                                statusDiv.innerHTML += `<br><small>HTTP ${response.status}: ${text}</small>`;
                            });
                        }
                    })
                    .catch(error => {
                        statusDiv.innerHTML += `<br><small>Network error: ${error.message}</small>`;
                    });
            };
            
            img.src = url;
        }

        // Run tests
        document.addEventListener('DOMContentLoaded', function() {
            // API Key info
            const apiKeyInfo = document.getElementById('api-key-info');
            apiKeyInfo.innerHTML = `
                <p><strong>API Key:</strong> <code>${API_KEY.substring(0, 10)}...${API_KEY.substring(API_KEY.length - 4)}</code></p>
                <p><strong>Domain:</strong> <code>${window.location.hostname}</code></p>
                <p><strong>Protocol:</strong> <code>${window.location.protocol}</code></p>
            `;

            // Test 1: Simple map
            const test1Url = createMapUrl(testAddress);
            testImage('test1-img', 'test1-url', 'test1-status', test1Url, 'Simple Map');

            // Test 2a: Roadmap
            const test2aUrl = createMapUrl(testAddress, 'roadmap');
            testImage('test2a-img', 'test2a-url', 'test2a-status', test2aUrl, 'Roadmap');

            // Test 2b: Hybrid
            const test2bUrl = createMapUrl(testAddress, 'hybrid');
            testImage('test2b-img', 'test2b-url', 'test2b-status', test2bUrl, 'Hybrid');
        });
    </script>
</body>
</html>
