#!/bin/bash

# JobbLogg Convex URL Fix Script - DEPRECATED
# ⚠️  WARNING: This script is deprecated and should not be used.
# ⚠️  Environment variables are now managed exclusively through GitHub Actions
# ⚠️  secrets and <PERSON><PERSON> build arguments to prevent configuration conflicts.

echo "❌ This script is deprecated and disabled to prevent configuration conflicts."
echo "🔧 Environment variables are now managed through GitHub Actions secrets only."
echo "📋 See PRODUCTION_CONVEX_URL_ERROR_AUDIT.md for details."
exit 1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🔧 JobbLogg Convex URL Fix"
print_status "=========================="

# Step 1: Check current environment configuration
print_status "Step 1: Checking current environment configuration"
print_status "=================================================="

if [ -f ".env.production" ]; then
    print_status "Found .env.production file"
    
    # Check if VITE_CONVEX_URL is set
    if grep -q "VITE_CONVEX_URL" .env.production; then
        current_url=$(grep "VITE_CONVEX_URL" .env.production | cut -d'=' -f2)
        print_status "Current VITE_CONVEX_URL: $current_url"
        
        if [ -z "$current_url" ] || [ "$current_url" = "" ]; then
            print_error "VITE_CONVEX_URL is empty!"
        elif [[ ! "$current_url" =~ ^https?:// ]]; then
            print_error "VITE_CONVEX_URL is not an absolute URL: $current_url"
        else
            print_success "VITE_CONVEX_URL looks valid: $current_url"
        fi
    else
        print_error "VITE_CONVEX_URL not found in .env.production"
    fi
else
    print_warning ".env.production file not found"
fi

# Step 2: Test Convex endpoints
print_status "Step 2: Testing Convex endpoints"
print_status "================================="

endpoints=(
    "https://api.jobblogg.no"
    "https://standing-aardvark-575.convex.cloud"
)

for endpoint in "${endpoints[@]}"; do
    print_status "Testing endpoint: $endpoint"
    
    if curl -f -s --max-time 10 "$endpoint" >/dev/null 2>&1; then
        print_success "✅ $endpoint is accessible"
    else
        print_warning "⚠️  $endpoint is not accessible or slow"
    fi
done

# Step 3: Check what's running on port 3211 (api.jobblogg.no proxy target)
print_status "Step 3: Checking port 3211 (api.jobblogg.no proxy target)"
print_status "========================================================"

if lsof -i :3211 >/dev/null 2>&1; then
    print_status "Port 3211 is in use:"
    lsof -i :3211 2>/dev/null || netstat -tlnp 2>/dev/null | grep ":3211 " || true
    
    # Test local connection
    if curl -f -s --max-time 5 "http://localhost:3211" >/dev/null 2>&1; then
        print_success "✅ localhost:3211 is responding"
    else
        print_warning "⚠️  localhost:3211 is not responding"
    fi
else
    print_error "❌ Port 3211 is not in use - api.jobblogg.no proxy will fail"
fi

# Step 4: Fix environment configuration
print_status "Step 4: Fixing environment configuration"
print_status "========================================"

# Create or update .env.production with correct Convex URL
print_status "Setting VITE_CONVEX_URL to https://standing-aardvark-575.convex.cloud"

if [ -f ".env.production" ]; then
    # Update existing file
    if grep -q "VITE_CONVEX_URL" .env.production; then
        sed -i 's|VITE_CONVEX_URL=.*|VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud|' .env.production
        print_success "Updated VITE_CONVEX_URL in existing .env.production"
    else
        echo "VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud" >> .env.production
        print_success "Added VITE_CONVEX_URL to .env.production"
    fi

    # Also update CONVEX_URL for consistency
    if grep -q "CONVEX_URL" .env.production; then
        sed -i 's|CONVEX_URL=.*|CONVEX_URL=https://standing-aardvark-575.convex.cloud|' .env.production
        print_success "Updated CONVEX_URL in existing .env.production"
    else
        echo "CONVEX_URL=https://standing-aardvark-575.convex.cloud" >> .env.production
        print_success "Added CONVEX_URL to .env.production"
    fi
else
    # Create new file from template
    if [ -f ".env.production.example" ]; then
        cp .env.production.example .env.production
        print_success "Created .env.production from template"
    else
        print_status "Creating minimal .env.production file"
        cat > .env.production << 'EOF'
# JobbLogg Production Environment Variables
NODE_ENV=production
COMPOSE_ENV=prod
BUILD_TARGET=production

# Convex Configuration
CONVEX_DEPLOYMENT=prod:jobblogg
CONVEX_URL=https://standing-aardvark-575.convex.cloud
VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud

# Frontend Configuration
VITE_ALLOW_INDEXING=true
EOF
        print_success "Created minimal .env.production file"
    fi
fi

# Step 5: Restart containers with new configuration
print_status "Step 5: Restarting containers with new configuration"
print_status "===================================================="

if command -v docker >/dev/null 2>&1; then
    print_status "Restarting production containers..."
    
    # Stop current containers
    docker compose -f docker-compose.yml -f docker-compose.prod.yml down 2>/dev/null || true
    
    # Start with new environment
    docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
    
    print_status "Waiting for containers to start..."
    sleep 10
    
    # Check container status
    print_status "Container status:"
    docker compose -f docker-compose.yml -f docker-compose.prod.yml ps
    
else
    print_warning "Docker not available - manual restart required"
fi

# Step 6: Final verification
print_status "Step 6: Final verification"
print_status "=========================="

print_status "Current .env.production Convex configuration:"
if [ -f ".env.production" ]; then
    grep -E "(CONVEX|convex)" .env.production || echo "No Convex configuration found"
else
    print_error ".env.production file missing"
fi

print_status "Testing frontend connectivity..."
if curl -f -s --max-time 10 "http://localhost:5174/" >/dev/null 2>&1; then
    print_success "✅ Frontend is responding on port 5174"
else
    print_warning "⚠️  Frontend not responding on port 5174"
fi

print_success "🎉 Convex URL fix completed!"
print_status ""
print_status "Next steps:"
print_status "1. Check https://jobblogg.no to see if the error is resolved"
print_status "2. Check browser console for any remaining errors"
print_status "3. If issues persist, check container logs:"
print_status "   docker compose -f docker-compose.yml -f docker-compose.prod.yml logs frontend"
