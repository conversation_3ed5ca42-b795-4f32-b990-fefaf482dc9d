# JobbLogg Staging Environment Variables
# Standardized format matching .env.example structure

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=staging
BUILD_ENV=staging
BUILD_TARGET=production
COMPOSE_ENV=staging

# =============================================================================
# PORT CONFIGURATION
# =============================================================================
FRONTEND_PORT=5175
CONVEX_PORT=3211

# =============================================================================
# CONVEX BACKEND CONFIGURATION
# =============================================================================
# Convex deployment (staging uses dev deployment)
CONVEX_DEPLOYMENT=dev:enchanted-quail-174

# Convex API URL (staging uses cloud deployment)
CONVEX_URL=https://enchanted-quail-174.convex.cloud
VITE_CONVEX_URL=https://enchanted-quail-174.convex.cloud

# =============================================================================
# CLERK AUTHENTICATION (STAGING)
# =============================================================================
# Clerk staging publishable key
VITE_CLERK_PUBLISHABLE_KEY=pk_test_bG92ZWQtZG9yeS04Ni5jbGVyay5hY2NvdW50cy5kZXYk

# =============================================================================
# GOOGLE MAPS INTEGRATION
# =============================================================================
# Google Maps API key (shared across environments)
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs

# =============================================================================
# STRIPE PAYMENT PROCESSING (STAGING/TEST)
# =============================================================================
# Stripe test publishable key
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51QuHVWRqXwHRnsDwFyefP4DJfEDG9Ti42UkWO7Y5nWmSDZbZtVLWfgDmmAP3YnYYb8905qIhtDDB8UUPLDjaUk9F00snevRBNh

# Stripe test secret key - SERVER ONLY
STRIPE_SECRET_KEY=sk_test_51QuHVWRqXwHRnsDwtLPJ2Qd310QWPUvfvYKmxE4WPmC6ERPHCGfkdKgZp9xNZs3uPhUzGKQsmqytsgBdnXEClv3u00sKnCLi9T

# Stripe webhook secret - SERVER ONLY (staging-specific webhook)
STRIPE_WEBHOOK_SECRET=whsec_rVBZ1OgjThjWrLxpnsC3A0HKDq979d9f

# =============================================================================
# EMAIL SERVICE (RESEND) - STAGING
# =============================================================================
# Resend API key for staging notifications - SERVER ONLY
RESEND_API_KEY=re_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL

# =============================================================================
# SEO AND INDEXING
# =============================================================================
# Prevent search engines from indexing staging environment
VITE_ALLOW_INDEXING=false
