# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Environment files (keep .env.example for template)
.env
.env.local
.env.development
.env.production
.env.netlify
# Note: .env.staging is committed for server deployment

# Docker files (no longer needed for Netlify deployment)
docker-compose*.yml
Dockerfile*
docker/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
