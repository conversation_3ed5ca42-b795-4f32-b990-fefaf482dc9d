import React, { useState } from 'react';
import { useClerk } from '@clerk/clerk-react';
import { Link } from 'react-router-dom';

interface WaitlistFormProps {
  onSuccess?: () => void;
}

export const WaitlistForm: React.FC<WaitlistFormProps> = ({ onSuccess }) => {
  const clerk = useClerk();
  
  // Form state
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasExistingAccount, setHasExistingAccount] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('E-postadresse er påkrevd');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Vennligst oppgi en gyldig e-postadresse');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setHasExistingAccount(false);

    try {
      // Use Clerk's official joinWaitlist method
      await clerk.joinWaitlist({
        emailAddress: email,
      });
      
      setIsSubmitted(true);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error('Waitlist submission error:', err);
      
      // Handle specific error cases based on Clerk's error codes
      const errorCode = err.errors?.[0]?.code || err.code;
      const errorMessage = err.errors?.[0]?.message || err.message || '';
      const longMessage = err.errors?.[0]?.longMessage || '';
      
      if (errorCode === 'form_identifier_exists' ||
          errorCode === 'form_identifier_exists__email_address' ||
          errorMessage.includes('identifier already exists') ||
          longMessage.includes('identifier already exists')) {
        setError('Denne e-postadressen er allerede registrert på ventelisten');
      } else if (errorCode === 'form_identifier_not_allowed') {
        setError('Denne e-postadressen er ikke tillatt');
      } else if (errorCode === 'user_already_exists' ||
                 errorMessage.includes('already exists') ||
                 errorMessage.includes('user exists') ||
                 longMessage.includes('user exists')) {
        setError('Det finnes allerede en brukerkonto med denne e-postadressen.');
        setHasExistingAccount(true);
      } else if (errorCode === 'waitlist_full' || errorMessage.includes('waitlist is full')) {
        setError('Ventelisten er for øyeblikket full. Prøv igjen senere.');
      } else if (errorCode === 'invalid_email_address' || errorMessage.includes('invalid email')) {
        setError('Vennligst oppgi en gyldig e-postadresse');
      } else if (errorMessage.includes('rate limit') || errorCode === 'rate_limit_exceeded') {
        setError('For mange forsøk. Vennligst vent litt før du prøver igjen.');
      } else {
        setError('Noe gikk galt. Vennligst prøv igjen senere.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="space-y-6">
        {/* Success State */}
        <div className="text-center space-y-6">
          <div className="w-16 h-16 bg-jobblogg-accent/10 rounded-full flex items-center justify-center mx-auto animate-scale-in">
            <svg className="w-8 h-8 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div className="space-y-4">
            <h3 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
              Velkommen til ventelisten! 🎉
            </h3>
            <div className="space-y-3">
              <p className="text-sm sm:text-base text-jobblogg-text-strong font-medium">
                Du er nå registrert med e-postadressen:
              </p>
              <p className="text-jobblogg-accent font-semibold bg-jobblogg-accent/5 px-4 py-3 rounded-lg text-sm sm:text-base break-all">
                {email}
              </p>
            </div>
            <p className="text-sm sm:text-base text-jobblogg-text-medium leading-relaxed">
              Vi sender deg en e-post så snart du kan komme i gang med JobbLogg.
              Hold øye med innboksen din!
            </p>
          </div>
          <div className="pt-2 space-y-4">
            <Link
              to="/"
              className="inline-flex items-center px-6 py-3 bg-jobblogg-accent hover:bg-jobblogg-accent-dark text-white font-semibold rounded-xl transition-all duration-200 min-h-[44px] transform active:scale-[0.98]"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              Gå til forsiden
            </Link>
            <div className="text-xs sm:text-sm text-jobblogg-text-muted">
              Du vil motta en bekreftelse på e-post innen kort tid
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-3">
        <h1 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
          Bli med på ventelisten
        </h1>
        <p className="text-sm sm:text-base text-jobblogg-text-medium leading-relaxed">
          Vi tar imot nye brukere gradvis. Registrer deg for å bli varslet når du kan komme i gang med JobbLogg!
        </p>
      </div>

      {/* Waitlist Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium text-jobblogg-text-strong">
            E-postadresse
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
            placeholder="<EMAIL>"
            autoComplete="email"
            required
            disabled={isSubmitting}
          />
          {error && (
            <div className="mt-3">
              <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
                <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-red-600">{error}</p>
              </div>
              {hasExistingAccount && (
                <div className="mt-3 pl-3">
                  <Link
                    to="/sign-in"
                    className="inline-flex items-center text-sm font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] px-2 py-2"
                  >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Gå til innlogging
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-[44px] transform active:scale-[0.98]"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Registrerer...</span>
            </>
          ) : (
            <span>Bli med på ventelisten</span>
          )}
        </button>
      </form>

      {/* Footer Links */}
      <div className="text-center space-y-4">
        <div className="text-sm sm:text-base text-jobblogg-text-medium">
          Har du allerede konto?{' '}
          <Link
            to="/sign-in"
            className="font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] inline-flex items-center px-2 py-1"
          >
            Logg inn her
          </Link>
        </div>

        {/* Trust indicator */}
        <div className="flex items-center justify-center space-x-2 text-xs text-jobblogg-text-muted">
          <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <span>Sikret av Clerk</span>
        </div>
      </div>
    </div>
  );
};
