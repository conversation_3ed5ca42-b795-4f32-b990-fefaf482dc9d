import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { JobbLoggLogo } from '../ui';

interface AuthHeaderProps {
  /** Link to navigate to when logo is clicked */
  homeLink?: string;
  /** Additional action link (e.g., "Logg inn" on sign-up page) */
  actionLink?: {
    to: string;
    text: string;
    ariaLabel?: string;
  };
}

/**
 * Minimal header for authentication pages
 * Following the "Action-First" mobile design principles
 */
export const AuthHeader: React.FC<AuthHeaderProps> = ({ 
  homeLink = "/", 
  actionLink 
}) => {
  return (
    <header className="sticky top-0 z-40 bg-white border-b border-jobblogg-border shadow-sm">
      <div className="flex items-center justify-between h-16 px-4 sm:px-6 max-w-4xl mx-auto">
        {/* Logo */}
        <Link
          to={homeLink}
          className="hover:opacity-80 transition-opacity focus-ring rounded-lg"
          aria-label="JobbLogg hjem"
        >
          <JobbLoggLogo
            variant="horizontal"
            size="md"
            className="hover:scale-105 transition-transform duration-200"
          />
        </Link>

        {/* Action Link */}
        {actionLink && (
          <Link
            to={actionLink.to}
            className="text-sm font-medium text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors focus-ring rounded-lg px-3 py-2"
            aria-label={actionLink.ariaLabel}
          >
            {actionLink.text}
          </Link>
        )}
      </div>
    </header>
  );
};

export default AuthHeader;
