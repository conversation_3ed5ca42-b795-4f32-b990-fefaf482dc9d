import React from 'react';

interface SocialProofProps {
  /** Number of users/companies */
  count: string;
  /** Description text */
  description: string;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Compact social proof component for authentication pages
 */
export const SocialProof: React.FC<SocialProofProps> = ({ 
  count, 
  description, 
  className = "" 
}) => {
  return (
    <div className={`text-center py-4 ${className}`}>
      <div className="inline-flex items-center gap-2 px-4 py-2 bg-jobblogg-success/10 rounded-full">
        <div className="w-2 h-2 bg-jobblogg-success rounded-full animate-pulse"></div>
        <span className="text-sm font-medium text-jobblogg-success">
          Allerede brukt av {count} {description}
        </span>
      </div>
    </div>
  );
};

export default SocialProof;
