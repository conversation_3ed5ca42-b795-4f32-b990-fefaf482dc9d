import React, { useState } from 'react';
import { useClerk } from '@clerk/clerk-react';
import { Link, useNavigate } from 'react-router-dom';

interface CustomSignUpFormProps {
  redirectUrl?: string;
  onSuccess?: () => void;
}

export const CustomSignUpForm: React.FC<CustomSignUpFormProps> = ({
  redirectUrl = '/',
  onSuccess,
}) => {
  const clerk = useClerk();
  const navigate = useNavigate();
  
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [needsVerification, setNeedsVerification] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  const validateForm = () => {
    if (!email.trim() || !password.trim() || !firstName.trim() || !lastName.trim()) {
      setError('Vennligst fyll ut alle feltene');
      return false;
    }

    if (password !== confirmPassword) {
      setError('Passordene stemmer ikke overens');
      return false;
    }

    if (password.length < 8) {
      setError('Passordet må være minst 8 tegn langt');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Create sign-up attempt
      const signUpAttempt = await clerk.client?.signUp.create({
        emailAddress: email,
        password,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
      });

      if (signUpAttempt?.status === 'complete') {
        // Sign-up successful - set active session
        await clerk.setActive({ session: signUpAttempt.createdSessionId });
        
        if (onSuccess) {
          onSuccess();
        } else {
          navigate(redirectUrl);
        }
      } else if (signUpAttempt?.status === 'missing_requirements') {
        // Need email verification
        await signUpAttempt.prepareEmailAddressVerification({ strategy: 'email_code' });
        setNeedsVerification(true);
      } else {
        setError('Registrering krever ytterligere steg');
      }
    } catch (err: any) {
      console.error('Sign-up error:', err);
      
      // Handle specific error cases
      const errorCode = err.errors?.[0]?.code || err.code;
      const errorMessage = err.errors?.[0]?.message || err.message || '';
      
      if (errorCode === 'form_identifier_exists' || errorMessage.includes('already exists')) {
        setError('Det finnes allerede en konto med denne e-postadressen');
      } else if (errorCode === 'form_password_pwned' || errorMessage.includes('password')) {
        setError('Dette passordet er ikke sikkert nok. Velg et annet passord');
      } else if (errorCode === 'form_identifier_not_allowed' || errorMessage.includes('not allowed')) {
        setError('Denne e-postadressen er ikke tillatt');
      } else if (errorMessage.includes('rate limit') || errorCode === 'too_many_requests') {
        setError('For mange forsøk. Vent litt før du prøver igjen');
      } else {
        setError('Registrering feilet. Prøv igjen senere');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!verificationCode.trim()) {
      setError('Vennligst skriv inn verifiseringskoden');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const signUpAttempt = await clerk.client?.signUp.attemptEmailAddressVerification({
        code: verificationCode,
      });

      if (signUpAttempt?.status === 'complete') {
        await clerk.setActive({ session: signUpAttempt.createdSessionId });
        
        if (onSuccess) {
          onSuccess();
        } else {
          navigate(redirectUrl);
        }
      } else {
        setError('Verifisering feilet. Sjekk koden og prøv igjen');
      }
    } catch (err: any) {
      console.error('Verification error:', err);
      setError('Ugyldig verifiseringskode. Prøv igjen');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (needsVerification) {
    return (
      <div className="space-y-6">
        {/* Verification Header */}
        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-jobblogg-accent/10 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-6 h-6 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
            Bekreft e-postadressen din
          </h1>
          <p className="text-sm sm:text-base text-jobblogg-text-medium">
            Vi har sendt en verifiseringskode til <strong>{email}</strong>
          </p>
        </div>

        {/* Verification Form */}
        <form onSubmit={handleVerification} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="verificationCode" className="block text-sm font-medium text-jobblogg-text-strong">
              Verifiseringskode
            </label>
            <input
              type="text"
              id="verificationCode"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted text-center text-lg tracking-widest min-h-[44px]"
              placeholder="123456"
              maxLength={6}
              autoComplete="one-time-code"
              required
              disabled={isSubmitting}
            />
          </div>

          {error && (
            <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
              <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-[44px] transform active:scale-[0.98]"
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Verifiserer...</span>
              </>
            ) : (
              <span>Bekreft konto</span>
            )}
          </button>
        </form>

        <div className="text-center text-sm sm:text-base text-jobblogg-text-medium">
          Ikke mottatt koden?{' '}
          <button
            onClick={() => setNeedsVerification(false)}
            className="font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] px-2 py-1"
          >
            Prøv igjen
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-3">
        <h1 className="text-xl sm:text-2xl font-bold text-jobblogg-text-strong">
          Opprett JobbLogg-konto
        </h1>
        <p className="text-sm sm:text-base text-jobblogg-text-medium">
          Kom i gang med profesjonell prosjektdokumentasjon
        </p>
      </div>

      {/* Sign-up Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Name Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="firstName" className="block text-sm font-medium text-jobblogg-text-strong">
              Fornavn
            </label>
            <input
              type="text"
              id="firstName"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
              placeholder="Ola"
              autoComplete="given-name"
              required
              disabled={isSubmitting}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="lastName" className="block text-sm font-medium text-jobblogg-text-strong">
              Etternavn
            </label>
            <input
              type="text"
              id="lastName"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
              placeholder="Nordmann"
              autoComplete="family-name"
              required
              disabled={isSubmitting}
            />
          </div>
        </div>

        {/* Email Field */}
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium text-jobblogg-text-strong">
            E-postadresse
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
            placeholder="<EMAIL>"
            autoComplete="email"
            required
            disabled={isSubmitting}
          />
        </div>

        {/* Password Fields */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium text-jobblogg-text-strong">
              Passord
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 pr-12 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
                placeholder="Minst 8 tegn"
                autoComplete="new-password"
                required
                disabled={isSubmitting}
                minLength={8}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted hover:text-jobblogg-text-strong transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center"
                disabled={isSubmitting}
                aria-label={showPassword ? 'Skjul passord' : 'Vis passord'}
              >
                {showPassword ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-jobblogg-text-strong">
              Bekreft passord
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-4 py-3 pr-12 text-base border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-all duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted min-h-[44px]"
                placeholder="Gjenta passordet"
                autoComplete="new-password"
                required
                disabled={isSubmitting}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted hover:text-jobblogg-text-strong transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center"
                disabled={isSubmitting}
                aria-label={showConfirmPassword ? 'Skjul passord' : 'Vis passord'}
              >
                {showConfirmPassword ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
            <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-[44px] transform active:scale-[0.98]"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Oppretter konto...</span>
            </>
          ) : (
            <span>Opprett konto</span>
          )}
        </button>
      </form>

      {/* Footer Links */}
      <div className="text-center space-y-4">
        <div className="text-sm sm:text-base text-jobblogg-text-medium">
          Har du allerede konto?{' '}
          <Link
            to="/sign-in"
            className="font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] inline-flex items-center px-2 py-1"
          >
            Logg inn her
          </Link>
        </div>

        <div className="text-xs sm:text-sm text-jobblogg-text-muted leading-relaxed">
          Ved å opprette konto godtar du våre{' '}
          <Link
            to="/terms-of-service"
            className="text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] inline-flex items-center px-1"
          >
            bruksvilkår
          </Link>{' '}
          og{' '}
          <Link
            to="/privacy-policy"
            className="text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] inline-flex items-center px-1"
          >
            personvernregler
          </Link>
        </div>

        {/* Trust indicator */}
        <div className="flex items-center justify-center space-x-2 text-xs text-jobblogg-text-muted">
          <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <span>Sikret av Clerk</span>
        </div>
      </div>
    </div>
  );
};
