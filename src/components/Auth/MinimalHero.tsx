import React from 'react';

interface MinimalHeroProps {
  /** Main headline */
  title: string;
  /** Optional subtitle */
  subtitle?: string;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Minimal hero section for authentication pages
 * Designed to be compact and action-focused
 */
export const MinimalHero: React.FC<MinimalHeroProps> = ({ 
  title, 
  subtitle, 
  className = "" 
}) => {
  return (
    <div className={`text-center space-y-3 ${className}`}>
      <h1 className="text-2xl sm:text-3xl font-bold text-jobblogg-text-strong leading-tight">
        {title}
      </h1>
      {subtitle && (
        <p className="text-base text-jobblogg-text-medium max-w-md mx-auto leading-relaxed">
          {subtitle}
        </p>
      )}
    </div>
  );
};

export default MinimalHero;
