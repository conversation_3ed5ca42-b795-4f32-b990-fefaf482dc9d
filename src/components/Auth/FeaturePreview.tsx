import React from 'react';

interface FeatureItem {
  icon: string;
  title: string;
  description: string;
}

interface FeaturePreviewProps {
  /** List of features to showcase */
  features: FeatureItem[];
  /** Additional CSS classes */
  className?: string;
}

/**
 * Compact feature preview for authentication pages
 * Shows key benefits without overwhelming the user
 */
export const FeaturePreview: React.FC<FeaturePreviewProps> = ({ 
  features, 
  className = "" 
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h2 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
          Profesjonell håndverksdokumentasjon
        </h2>
        <p className="text-sm text-jobblogg-text-medium">
          Alt du trenger for å holde kunder oppdatert
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {features.map((feature, index) => (
          <div 
            key={index}
            className="bg-white rounded-lg border border-jobblogg-border p-4 text-center space-y-2 hover:shadow-soft transition-shadow duration-200"
          >
            <div className="w-10 h-10 bg-jobblogg-primary/10 rounded-lg flex items-center justify-center mx-auto">
              <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={feature.icon} />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-jobblogg-text-strong">
              {feature.title}
            </h3>
            <p className="text-xs text-jobblogg-text-medium leading-relaxed">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FeaturePreview;
