import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ConvexReactClient } from 'convex/react'
import { ConvexProviderWithClerk } from 'convex/react-clerk'
import { Clerk<PERSON>rovider, useAuth } from '@clerk/clerk-react'
import { nbNO } from '@clerk/localizations'
import './index.css'
import App from './App.tsx'


// Combine official Norwegian localization with our custom additions
const jobbloggLocalization = {
  ...nbNO,
  // Add our custom UserButton menu items
  userButtonPopoverActionButton__manageAccount: '<PERSON> Brukerkonto',
  userButtonPopoverActionButton__signOut: 'Logg ut',
  userButtonPopoverActionButton__companyProfile: 'Bedriftsprofil',
};
import { PerformanceMonitor } from './utils/lazyLoading'
import { setupSEOTags } from './utils/seo'

// Comprehensive Convex URL validation with detailed error messages
const url = import.meta.env.VITE_CONVEX_URL as string

if (!url || !/^https:\/\//.test(url)) {
  throw new Error("Missing or invalid VITE_CONVEX_URL")
}

// Valgfri strammere prod-sjekk uten å nevne konkrete domener:
if (import.meta.env.PROD && !/^https:\/\/.*\.convex\.cloud/.test(url)) {
  throw new Error("Wrong Convex URL in PROD build")
}

export const convex = new ConvexReactClient(url)
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY as string

if (!clerkPubKey) {
  throw new Error("Missing Clerk Publishable Key")
}

// Register service worker for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('[PWA] Service Worker registered successfully:', registration.scope);
      })
      .catch((error) => {
        console.log('[PWA] Service Worker registration failed:', error);
      });
  });
}

// Initialize performance monitoring
PerformanceMonitor.monitorWebVitals();

// Setup SEO tags (prevents indexing on staging environments)
setupSEOTags();



createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ClerkProvider
      publishableKey={clerkPubKey}
      localization={jobbloggLocalization as any}
      waitlistUrl="/sign-up"
    >
      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
        <App />
      </ConvexProviderWithClerk>
    </ClerkProvider>
  </StrictMode>,
)
