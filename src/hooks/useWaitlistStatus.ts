import { useState, useEffect } from 'react';
import { useClerk } from '@clerk/clerk-react';

interface WaitlistStatus {
  isWaitlistEnabled: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook to check if waitlist is enabled in the current Clerk instance
 * This determines whether to show regular sign-up or waitlist form
 */
export const useWaitlistStatus = (): WaitlistStatus => {
  const [status, setStatus] = useState<WaitlistStatus>({
    isWaitlistEnabled: false,
    isLoading: true,
    error: null,
  });
  
  const clerk = useClerk();

  useEffect(() => {
    const checkWaitlistStatus = () => {
      try {
        if (!clerk.loaded) {
          return; // Wait for Clerk to load
        }

        // Simple check: if joinWaitlist method exists, assume waitlist is enabled
        // This is a safer approach than trying to create test sign-ups
        const hasWaitlistMethod = typeof clerk.joinWaitlist === 'function';

        setStatus({
          isWaitlistEnabled: hasWaitlistMethod,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        console.error('Error checking waitlist status:', error);
        setStatus({
          isWaitlistEnabled: false,
          isLoading: false,
          error: 'Kunne ikke sjekke waitlist-status',
        });
      }
    };

    checkWaitlistStatus();
  }, [clerk.loaded, clerk.joinWaitlist]);

  return status;
};
