import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { AuthHeader, SocialProof, CustomSignInForm } from '../../components/Auth';

const SignIn: React.FC = () => {
  const [searchParams] = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile-First Header */}
      <AuthHeader
        actionLink={{
          to: "/sign-up",
          text: "Registrer deg",
          ariaLabel: "Gå til registreringssiden"
        }}
      />

      {/* Main Content Container */}
      <main className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-md mx-auto space-y-8">

          {/* Custom Sign-in Form */}
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
            <CustomSignInForm redirectUrl={redirectUrl} />
          </div>

          {/* Social Proof */}
          <SocialProof
            count="100+"
            description="håndverksbedrifter"
          />

        </div>
      </main>
    </div>
  );
};

export default SignIn;


