import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { Link } from 'react-router-dom';
import { JobbLoggLogo } from '../../components/ui';
import { CustomSignUpForm, WaitlistForm } from '../../components/Auth';
import { useWaitlistStatus } from '../../hooks';

const SignUp: React.FC = () => {
  const [searchParams] = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';
  const { isWaitlistEnabled, isLoading, error: waitlistError } = useWaitlistStatus();

  // Determine which form to show based on waitlist status
  const renderAuthForm = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-accent"></div>
        </div>
      );
    }

    if (waitlistError) {
      return (
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">Kunne ikke laste registreringsform</p>
          <p className="text-jobblogg-text-muted text-sm">{waitlistError}</p>
        </div>
      );
    }

    // Show waitlist form if waitlist is enabled, otherwise show regular sign-up
    if (isWaitlistEnabled) {
      return <WaitlistForm />;
    } else {
      return <CustomSignUpForm redirectUrl={redirectUrl} />;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile-First Header */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex items-center justify-between max-w-md mx-auto">
          <Link
            to="/"
            className="inline-flex items-center"
            aria-label="Gå til forsiden"
          >
            <JobbLoggLogo size="md" />
          </Link>
          <Link
            to="/sign-in"
            className="text-sm font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200 min-h-[44px] flex items-center px-3 py-2"
          >
            Logg inn
          </Link>
        </div>
      </div>

      {/* Main Content Container */}
      <main className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-md mx-auto space-y-8">
          {/* Dynamic Auth Form */}
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
            {renderAuthForm()}
          </div>

          {/* Footer Links */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-6 text-sm text-jobblogg-text-muted">
              <Link
                to="/privacy-policy"
                className="hover:text-jobblogg-accent transition-colors duration-200 min-h-[44px] flex items-center px-2 py-1"
              >
                Personvern
              </Link>
              <span className="text-jobblogg-border">•</span>
              <Link
                to="/terms-of-service"
                className="hover:text-jobblogg-accent transition-colors duration-200 min-h-[44px] flex items-center px-2 py-1"
              >
                Bruksvilkår
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SignUp;
