# JobbLogg Netlify Deployment Guide

## Migration fra Hetzner/Docker til Netlify

### 1. Netlify Site Setup

1. **Koble til GitHub repo**:
   - <PERSON><PERSON> til [Netlify Dashboard](https://app.netlify.com)
   - <PERSON><PERSON>k "New site from Git"
   - Velg GitHub og autorisér
   - Velg `djrobbieh/JobbLogg` repository

2. **Build Settings** (allerede konfigurert):
   ```
   Build command: npm run build
   Publish directory: dist
   ```

### 2. Environment Variables

Legg til disse i Netlify Dashboard under **Site settings > Environment variables**:

```bash
# Convex (Production)
VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud

# Clerk (Production)
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k

# Google Maps
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs

# Stripe (Production) - OPPDATER MED DINE LIVE KEYS
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_[DIN_PRODUCTION_KEY]

# SEO
VITE_ALLOW_INDEXING=true
```

### 3. Domain Setup

1. **Custom Domain**:
   - Gå til **Site settings > Domain management**
   - Legg til `jobblogg.no` som custom domain
   - Netlify vil automatisk sette opp SSL sertifikat

2. **DNS Configuration**:
   - Oppdater DNS hos din domain provider:
   ```
   Type: CNAME
   Name: www
   Value: [your-site-name].netlify.app
   
   Type: A
   Name: @
   Value: ********* (Netlify Load Balancer)
   ```

### 4. Deployment Process

1. **Automatisk deployment**:
   - Push til `main` branch trigger automatisk deployment
   - Netlify bygger og deployer automatisk

2. **Manual deployment**:
   - Gå til **Deploys** tab i Netlify Dashboard
   - Klikk "Trigger deploy" > "Deploy site"

### 5. Monitoring og Logs

- **Build logs**: Se under **Deploys** tab
- **Function logs**: Under **Functions** tab (hvis du legger til Netlify Functions senere)
- **Analytics**: Tilgjengelig under **Analytics** tab

### 6. Cleanup - Filer som kan fjernes

Etter vellykket Netlify deployment kan du fjerne:

```bash
# Docker-relaterte filer
docker/
docker-compose*.yml
Dockerfile*

# Deployment scripts
deploy*.sh
scripts/docker-*.sh
scripts/setup-*.sh

# Server-spesifikke config
.env.staging
.env.production
```

### 7. Fordeler med Netlify

- ✅ Automatisk SSL sertifikater
- ✅ Global CDN
- ✅ Automatisk deployment fra Git
- ✅ Branch previews
- ✅ Form handling (hvis nødvendig senere)
- ✅ Serverless functions support
- ✅ Ingen server maintenance

### 8. Testing

1. **Lokal testing før deployment**:
   ```bash
   npm run build:netlify
   npm run preview
   ```

2. **Production testing**:
   - Test alle hovedfunksjoner på jobblogg.netlify.app
   - Verifiser at alle environment variables fungerer
   - Test Clerk authentication
   - Test Stripe integration
   - Test Convex database connection

### 9. Rollback Plan

Hvis noe går galt:
1. Gå til **Deploys** i Netlify Dashboard
2. Finn forrige fungerende deployment
3. Klikk "Publish deploy" på den versjonen
